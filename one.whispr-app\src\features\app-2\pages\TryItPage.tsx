/**
 * TryItPage Component
 * Minimal try-it page with placeholder content
 */

import React from 'react';
import { useTryIt } from '../contexts/TryItContext';
import { Card, CardContent, CardHeader, CardTitle } from '@src/components/ui/card';
import { Button } from '@src/components/ui/button';

interface TryItPageProps {
  onNext?: () => void;
  onPrevious?: () => void;
  isActive?: boolean;
}

export function TryItPage({ onNext, onPrevious, isActive = true }: TryItPageProps) {
  const { state } = useTryIt();

  const handleComplete = () => {
    console.log('🎉 Setup completed!');
    if (onNext) {
      onNext();
    }
  };

  return (
    <div className="h-full flex items-center justify-center px-6">
      <div className="max-w-md w-full space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-semibold text-foreground">Try It Out</h2>
          <p className="text-muted-foreground">
            Test your complete setup with a real transcription
          </p>
        </div>

        {/* Main Content Card */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-center">Coming Soon</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center p-6">
              <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚧</span>
              </div>
              <p className="text-muted-foreground text-sm mb-4">
                Transcription testing functionality will be added here.
              </p>
            </div>

            {/* Navigation */}
            <div className="flex justify-between pt-4">
              {onPrevious ? (
                <Button
                  onClick={onPrevious}
                  variant="outline"
                  size="sm"
                  className="gap-1"
                >
                  ← Previous
                </Button>
              ) : (
                <div />
              )}

              <Button
                onClick={handleComplete}
                size="sm"
                className="bg-primary hover:bg-primary/90"
              >
                Complete Setup
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 