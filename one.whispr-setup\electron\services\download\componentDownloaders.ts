import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { getLauncherWindow } from '../../window';
import { BACKEND_UPDATES } from '../../constants';
import { ArchiveExtractor } from './archiveExtractor';
import { VersionManager } from '../version/versionManager';
import { MicrosoftStoreHandler } from '../microsoft/storeHandler';

interface BackendDownloadProgress {
  type: 'runtime' | 'scripts';
  progress: number;
  speed: number;
  eta: number;
  status: string;
}

/**
 * Handles downloading of individual backend components
 */
export class ComponentDownloaders {
  private backendPath: string;
  private abortController: AbortController | null = null;
  private archiveExtractor: ArchiveExtractor;
  private versionManager: VersionManager;

  constructor(backendPath: string, microsoftStoreHandler?: MicrosoftStoreHandler) {
    this.backendPath = backendPath;
    this.archiveExtractor = new ArchiveExtractor(backendPath);
    this.versionManager = new VersionManager(backendPath, microsoftStoreHandler);
  }

  setAbortController(controller: AbortController | null): void {
    this.abortController = controller;
  }

  /**
   * Download and extract base runtime
   */
  async downloadRuntime(): Promise<void> {
    const runtimeInfo = await this.versionManager.fetchVersionInfo('runtime');
    if (!runtimeInfo) {
      throw new Error('Failed to fetch runtime version info');
    }
    
    this.sendProgress('runtime', 0, 0, 0, 'Downloading CUDA runtime...');
    
    // Clean up old runtime files first (but preserve scripts)
    await this.cleanupRuntimeFiles();
    
    // Download the 7z archive
    const archivePath = path.join(this.backendPath, 'OneWhispr-Runtime-Base.7z');
    await this.downloadFile(BACKEND_UPDATES.runtime.archiveUrl, archivePath, 'runtime');
    
    this.sendProgress('runtime', 90, 0, 0, 'Extracting CUDA runtime...');

    // Extract the archive
    await this.archiveExtractor.extract7z(archivePath, this.backendPath);

    // Clean up info files if version files exist
    await this.archiveExtractor.cleanupInfoFiles();

    // Clean up archive
    await fs.remove(archivePath);
    
    // Save version info
    await fs.writeJson(path.join(this.backendPath, 'runtime-version.json'), {
      version: runtimeInfo.version,
      releaseDate: runtimeInfo.releaseDate,
      checksum: runtimeInfo.checksum,  // Save checksum for future comparisons
      installedAt: new Date().toISOString()
    });
    
    this.sendProgress('runtime', 100, 0, 0, 'CUDA runtime installed');
  }

  /**
   * Download and extract scripts code
   */
  async downloadScripts(): Promise<void> {
    const scriptsInfo = await this.versionManager.fetchVersionInfo('scripts');
    if (!scriptsInfo) {
      throw new Error('Failed to fetch scripts version info');
    }

    this.sendProgress('scripts', 0, 0, 0, 'Downloading backend scripts...');

    // Create scripts directory
    const scriptsPath = path.join(this.backendPath, 'scripts');

    // Clean up old scripts files first
    await this.cleanupScriptsFiles();

    // Download the 7z archive
    const archivePath = path.join(this.backendPath, 'OneWhispr-Scripts.7z');
    await this.downloadFile(BACKEND_UPDATES.scripts.archiveUrl, archivePath, 'scripts');

    this.sendProgress('scripts', 90, 0, 0, 'Extracting backend scripts...');

    // Extract the archive to scripts directory
    await this.archiveExtractor.extract7z(archivePath, scriptsPath);

    // Clean up info files if version files exist
    await this.archiveExtractor.cleanupInfoFiles();

    // Clean up archive
    await fs.remove(archivePath);

    // Save version info in scripts directory
    await fs.writeJson(path.join(scriptsPath, 'scripts-version.json'), {
      version: scriptsInfo.version,
      releaseDate: scriptsInfo.releaseDate,
      installedAt: new Date().toISOString()
    });

    this.sendProgress('scripts', 100, 0, 0, 'Backend scripts updated');
  }



  /**
   * Clean up old scripts files
   */
  private async cleanupScriptsFiles(): Promise<void> {
    try {
      const scriptsPath = path.join(this.backendPath, 'scripts');

      // Remove the entire scripts directory if it exists
      if (fs.existsSync(scriptsPath)) {
        await fs.remove(scriptsPath);
        console.log('[BACKEND] Cleaned up old scripts directory');
      }

      // Ensure the scripts directory exists
      await fs.ensureDir(scriptsPath);
    } catch (error) {
      console.warn('[BACKEND] Error cleaning up scripts files:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Clean up old runtime files (but preserve scripts)
   */
  private async cleanupRuntimeFiles(): Promise<void> {
    try {
      console.log('[BACKEND] Cleaning up old runtime files (preserving scripts)...');
      
      // Same selective cleanup as Microsoft Store - remove only runtime files
      const runtimeFiles = ['One Whispr Backend.exe', '_internal', 'runtime-version.json'];

      for (const file of runtimeFiles) {
        const filePath = path.join(this.backendPath, file);
        if (fs.existsSync(filePath)) {
          await fs.remove(filePath);
          console.log(`[BACKEND] Removed: ${file}`);
        }
      }
    } catch (error) {
      console.warn('[BACKEND] Error cleaning up runtime files:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Download a file with progress reporting
   */
  private async downloadFile(url: string, filePath: string, type: 'runtime' | 'scripts'): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[BACKEND] Downloading ${type} from ${url}`);

        // Ensure directory exists
        await fs.ensureDir(path.dirname(filePath));

        // Create write stream
        const writer = fs.createWriteStream(filePath);

        // Track progress
        let downloadedBytes = 0;
        const startTime = Date.now();
        let lastUpdateTime = startTime;
        let lastBytes = 0;
        let speed = 0;

        // Download the file
        const response = await axios({
          method: 'get',
          url,
          responseType: 'stream',
          signal: this.abortController?.signal,
          timeout: 300000, // 5 minute timeout for large files
          headers: { 'User-Agent': 'OneWhispr-Setup/1.0.0' }
        });

        const totalSize = parseInt(response.headers['content-length'] || '0');

        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;

          // Calculate progress
          const progress = totalSize > 0 ? Math.min(90, Math.round((downloadedBytes / totalSize) * 90)) : 0;

          // Calculate speed and ETA
          const now = Date.now();
          const timeDiff = (now - lastUpdateTime) / 1000;

          if (timeDiff >= 1) { // Update every second
            speed = Math.round((downloadedBytes - lastBytes) / timeDiff);
            lastBytes = downloadedBytes;
            lastUpdateTime = now;
          }

          // Calculate ETA
          const remainingBytes = totalSize - downloadedBytes;
          const eta = speed > 0 ? Math.round(remainingBytes / speed) : 0;

          // Send progress update
          this.sendProgress(type, progress, speed, eta, `Downloading ${type}...`);
        });

        // Handle completion
        response.data.pipe(writer);

        writer.on('finish', resolve);
        writer.on('error', reject);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Send progress update to renderer
   */
  private sendProgress(type: 'runtime' | 'scripts', progress: number, speed: number, eta: number, status: string): void {
    const launcherWindow = getLauncherWindow();
    if (launcherWindow) {
      launcherWindow.webContents.send('backend:progress', {
        type,
        progress,
        speed,
        eta,
        status
      } as BackendDownloadProgress);
    }
  }
}
