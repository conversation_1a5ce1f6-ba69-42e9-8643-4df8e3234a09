import { ipcMain } from 'electron';

/**
 * Setup IPC handlers for download operations (main app and backend)
 */
export function setupDownloadIpc(services: any): void {
  // Remove existing handlers first to prevent duplicates during hot reload
  ipcMain.removeHandler('download:start');
  ipcMain.removeHandler('download:cancel');
  ipcMain.removeHandler('download:check-needed');
  ipcMain.removeHandler('download:get-manifest');
  ipcMain.removeHandler('backend:check-update');
  ipcMain.removeHandler('backend:download');
  ipcMain.removeHandler('backend:cancel');

  // Main app download handlers
  ipcMain.handle('download:start', async () => {
    return services.mainAppDownloader?.startDownload();
  });

  ipcMain.handle('download:cancel', () => {
    return services.mainAppDownloader?.cancelDownload();
  });

  ipcMain.handle('download:check-needed', async () => {
    return services.mainAppDownloader?.checkDownloadNeeded();
  });

  ipcMain.handle('download:get-manifest', () => {
    return services.mainAppDownloader?.getManifest();
  });

  // Backend download handlers
  ipcMain.handle('backend:check-update', async () => {
    return services.backendDownloader?.checkBackendUpdate();
  });

  ipcMain.handle('backend:download', async () => {
    return services.backendDownloader?.downloadBackend();
  });

  ipcMain.handle('backend:cancel', () => {
    return services.backendDownloader?.cancelDownload();
  });

  console.log('[IPC] Download handlers registered');
}
