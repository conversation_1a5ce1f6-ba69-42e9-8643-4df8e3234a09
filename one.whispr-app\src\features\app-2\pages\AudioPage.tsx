/**
 * AudioPage Component
 * Complete audio setup page matching app-latest AudioStep functionality exactly
 * Integrates AudioContext with sophisticated audio device management and monitoring
 */

import React, { useEffect, useRef } from 'react';
import { useAudio } from '../contexts/AudioContext';

import { AudioDeviceSelector } from '../components/audio/AudioDeviceSelector';
import { AudioLevelMonitor } from '../components/audio/AudioLevelMonitor';
import { Card, CardContent, CardHeader, CardTitle } from '@src/components/ui/card';
import { Button } from '@src/components/ui/button';
import { Alert, AlertDescription } from '@src/components/ui/alert';
import { HiRefresh, HiMicrophone, HiVolumeUp, HiExclamationCircle } from 'react-icons/hi';

interface AudioPageProps {
  onNext?: () => void;
  onPrevious?: () => void;
  isActive?: boolean; // Track if this step is currently active
}

export function AudioPage({ onNext, onPrevious, isActive = true }: AudioPageProps) {
  const { 
    state,
    loadDevices,
    selectInputDevice,
    selectOutputDevice,
    setUseDefaultInput,
    setUseDefaultOutput,
    startMonitoring,
    stopMonitoring,
    refreshDevices,
    clearError,
    canProceedToNext,
    isAudioReady
  } = useAudio();

  const { 
    inputDevices,
    outputDevices,
    selectedInputDevice,
    selectedOutputDevice,
    useDefaultInputDevice,
    useDefaultOutputDevice,
    audioLevel,
    isMonitoring,
    loading, 
    error 
  } = state;

  const isActiveRef = useRef<boolean>(isActive);

  // Update active state ref when prop changes
  useEffect(() => {
    isActiveRef.current = isActive;
  }, [isActive]);

  const handleRefresh = async () => {
    try {
      // Force refresh the devices (ignoring cache)
      console.log('🔄 Force refreshing audio devices...');
      
      if (window.electron?.sendCommand) {
        // Send force refresh command to Python to bypass cache
        const result = await window.electron.sendCommand('audio.refresh_devices', { force: true });
        
        if (result.success) {
          // Now refresh our local devices list
          await refreshDevices();
        } else {
          console.error('Python device refresh failed:', result.error);
          // Still try local refresh as fallback
          await refreshDevices();
        }
      } else {
        await refreshDevices();
      }
    } catch (error) {
      console.error('Failed to refresh audio devices:', error);
    }
  };

  const handleDeviceChange = async (deviceId: string, type: 'input' | 'output') => {
    try {
      if (type === 'input') {
        selectInputDevice(deviceId);
        // AudioContext handles device hot-swapping automatically - no manual restart needed
      } else {
        selectOutputDevice(deviceId);
      }
    } catch (error) {
      console.error(`Failed to change ${type} device:`, error);
    }
  };

  const handleUseDefaultChange = async (useDefault: boolean, type: 'input' | 'output') => {
    try {
      if (type === 'input') {
        setUseDefaultInput(useDefault);
        // AudioContext handles device hot-swapping automatically - no manual restart needed
      } else {
        setUseDefaultOutput(useDefault);
      }
    } catch (error) {
      console.error(`Failed to change ${type} default setting:`, error);
    }
  };

  // Handle step activation/deactivation (matching app-latest isActive behavior)
  useEffect(() => {
    if (isActive && selectedInputDevice && !isMonitoring && !loading) {
      // Step became active and we have a selected device - start monitoring
      startMonitoring();
    } else if (!isActive && isMonitoring) {
      // Step became inactive - stop monitoring
      stopMonitoring();
    }
  }, [isActive, selectedInputDevice, loading, isMonitoring, startMonitoring, stopMonitoring]);

  // Cleanup monitoring on unmount
  useEffect(() => {
    return () => {
      if (isMonitoring) {
        stopMonitoring();
      }
    };
  }, [isMonitoring, stopMonitoring]);

  return (
    <div className="h-full flex items-center justify-center px-6">
      <div className="max-w-4xl w-full space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-semibold text-foreground">Audio Setup</h2>
          <p className="text-muted-foreground">
            Select your audio devices and monitor levels
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <HiExclamationCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Two Cards Side by Side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-start">
          {/* Device Selection Card */}
          <Card className="self-center">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HiMicrophone className="w-5 h-5" />
                Device Selection
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 relative">
              {/* Loading Overlay */}
              {loading && (
                <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg">
                  <div className="text-center">
                    <HiRefresh className="w-6 h-6 animate-spin mx-auto mb-2 text-primary" />
                    <p className="text-sm text-muted-foreground">Loading audio devices...</p>
                  </div>
                </div>
              )}
              
              {/* Device Selection Content - Always visible */}
              <AudioDeviceSelector
                inputDevices={inputDevices}
                outputDevices={outputDevices}
                selectedInputDevice={selectedInputDevice}
                selectedOutputDevice={selectedOutputDevice}
                useDefaultInput={useDefaultInputDevice}
                useDefaultOutput={useDefaultOutputDevice}
                onSelectInputDevice={(deviceId: string) => handleDeviceChange(deviceId, 'input')}
                onSelectOutputDevice={(deviceId: string) => handleDeviceChange(deviceId, 'output')}
                onSetUseDefaultInput={(useDefault: boolean) => handleUseDefaultChange(useDefault, 'input')}
                onSetUseDefaultOutput={(useDefault: boolean) => handleUseDefaultChange(useDefault, 'output')}
                onRefresh={handleRefresh}
                loading={loading}
              />
            </CardContent>
          </Card>

          {/* Audio Levels Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HiVolumeUp className="w-5 h-5" />
                Audio Levels
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-1">
              <AudioLevelMonitor audioLevel={audioLevel} isMonitoring={isMonitoring} />
              
              {/* Navigation - moved inside Audio Levels card to match app-latest */}
              <div className="flex justify-between pt-4">
                <Button 
                  onClick={onPrevious} 
                  variant="outline" 
                  disabled={!onPrevious}
                >
                  Previous
                </Button>
                <Button 
                  onClick={onNext}
                  disabled={!canProceedToNext}
                >
                  Continue
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 