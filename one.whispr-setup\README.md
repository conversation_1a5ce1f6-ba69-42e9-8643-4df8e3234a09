# One Whispr Setup - Launcher & Updater

A lightweight Electron application that serves as an installer, updater, and launcher for the main One Whispr application.

## Features

- **Efficient Updates**: Downloads only changed files instead of the entire application
- **Progress Tracking**: Real-time progress bars and status updates with smart file counting
- **Microsoft Store Support**: Seamless installation from embedded resources for Store builds
- **Self-Updating**: Automatically updates itself using electron-updater
- **Simple UI**: Clean, minimal interface with One Whispr branding
- **Error Handling**: Graceful error handling with user-friendly messages
- **Modular Architecture**: Clean service-based architecture for maintainability

## Architecture

The setup app works in three phases:

1. **Checking**: Checks if updates are needed by comparing versions and file checksums
2. **Downloading**: Downloads only the files that have changed (bandwidth efficient) or copies from embedded resources (Microsoft Store)
3. **Launching**: Launches the main One Whispr application and closes itself

### Service Architecture

The application uses a clean service-based architecture:

- **Download Services**: Handle file downloads, version checking, and archive extraction
- **Launcher Services**: Manage app launching, process checking, and readiness validation
- **Microsoft Store Services**: Handle embedded resource extraction for Store builds
- **IPC Services**: Organized communication between main and renderer processes

## Development

```bash
# Install dependencies
npm install

# Run in development mode (skips downloads, launches directly)
npm run dev

# Run in development mode with built UI (skips downloads, launches directly)
npm run dev-preview

# Run in development mode with downloads enabled (downloads to .dist/main-app from VPS)
npm run dev-download

# Build for production
npm run build

# Build installer (exe)
npm run build:files:direct

# Build for Microsoft Store (appx)
npm run build:files:microsoft
```

## How It Works

### Direct Downloads
1. User downloads `OneWhisprSetup.exe` (small ~50-100MB)
2. Setup app checks for updates via manifest at `https://whispr.one/updates/main-app/latest/manifest.json`
3. Downloads only changed files to `%APPDATA%/OneWhispr/MainApp/`
4. Launches `One Whispr.exe` from the downloaded files
5. Setup app closes after confirming main app started

### Microsoft Store
1. User installs from Microsoft Store (includes embedded resources)
2. Setup app copies main app from embedded resources to `%APPDATA%/OneWhispr/MainApp/`
3. Shows "Copying files..." progress instead of file counts
4. Launches `One Whispr.exe` from the copied files
5. Setup app closes after confirming main app started

## Update System

- **Setup Updates**: Via electron-updater from `https://whispr.one/updates/setup/`
- **Main App Updates**: Via custom downloader from `https://whispr.one/updates/main-app/`
- **Microsoft Store**: Uses embedded resources, no external downloads required
- **Bandwidth Savings**: ~90% reduction for existing users (only changed files downloaded)

## Configuration

Update URLs and build constants are configured in `electron/constants.ts`:

```typescript
export const MAIN_APP_UPDATES = {
  baseUrl: 'https://whispr.one/updates/main-app',
  manifestUrl: 'https://whispr.one/updates/main-app/latest/manifest.json',
  // ...
};

export const IS_MICROSOFT = process.mas || process.windowsStore;
```

## Deployment

The app is deployed using GitHub Actions as described in `/.github/ELECTRON_DEPLOYMENT.md`.

### Build Types

- **Direct Build**: Standard Electron app with auto-updater and external downloads
- **Microsoft Store Build**: Includes embedded resources, uses copying instead of downloading
- **Development Build**: Skips downloads and launches directly for faster development