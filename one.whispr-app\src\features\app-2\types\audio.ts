/**
 * Audio Types
 * Types related to audio devices, monitoring, and audio management
 * Based on app-1 proven implementation and Python backend integration
 */

import type { DeviceType } from './core';

// ============================================================================
// AUDIO TYPES
// ============================================================================

export interface AudioDevice {
  id: string;
  name: string;
  type: DeviceType;
  isDefault: boolean;
  isAvailable: boolean;
  channels: number;
  sampleRate: number;
  hostApi: string;
}

export interface AudioLevel {
  microphone: number;
}

export interface AudioState {
  // Device lists (loaded from Python backend)
  inputDevices: AudioDevice[];
  outputDevices: AudioDevice[];
  
  // Selected devices (IDs stored in settings, converted to objects for display)
  selectedInputDevice: string;
  selectedOutputDevice: string;
  useDefaultInputDevice: boolean;
  useDefaultOutputDevice: boolean;
  
  // Real-time audio level (0-1 range)
  audioLevel: AudioLevel;
  
  // State flags
  isMonitoring: boolean;
  loading: boolean;
  error: string | null;
}

export interface AudioActions {
  loadDevices: () => Promise<void>;
  selectInputDevice: (deviceId: string) => void;
  selectOutputDevice: (deviceId: string) => void;
  setUseDefaultInput: (useDefault: boolean) => void;
  setUseDefaultOutput: (useDefault: boolean) => void;
  startMonitoring: () => Promise<void>;
  stopMonitoring: () => Promise<void>;
  refreshDevices: () => Promise<void>;
  clearError: () => void;
}

export interface AudioContextValue {
  state: AudioState;
  actions: AudioActions;
  canProceedToNext: boolean;
  isAudioReady: boolean;
} 