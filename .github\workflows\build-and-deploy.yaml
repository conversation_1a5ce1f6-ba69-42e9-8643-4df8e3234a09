name: Build and Deploy One Whispr

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      build_electron:
        description: 'Build Electron App'
        required: false
        default: false
        type: boolean
      force_python_deploy:
        description: 'Force deploy Python artifacts (runtime + scripts)'
        required: false
        default: false
        type: boolean
      force_scripts_only:
        description: 'Force deploy scripts only (quick update)'
        required: false
        default: false
        type: boolean
      rollback_to_version:
        description: 'Rollback to specific version (e.g., 1.2.1)'
        required: false
        type: string
      emergency_rollback:
        description: 'Emergency rollback to previous version'
        required: false
        default: false
        type: boolean

jobs:
  # Build site on self-hosted Windows runner
  build-site:
    runs-on: self-hosted
    if: github.event.inputs.rollback_to_version == '' && github.event.inputs.emergency_rollback != 'true' && !contains(github.event.head_commit.message, '[skip]')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'one.whispr-site/package-lock.json'

      - name: Install dependencies
        run: |
          cd one.whispr-site
          npm ci --prefer-offline --no-audit
        shell: powershell

      - name: Apply database migrations
        run: |
          cd one.whispr-site
          npx tsx scripts/apply-migrations.ts
        shell: powershell
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

      - name: Build site
        run: |
          cd one.whispr-site
          npm run build
        shell: powershell
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_SITE_URL: ${{ secrets.NEXT_PUBLIC_SITE_URL }}

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Install rsync on Windows
        run: |
          choco install rsync -y
          # Verify rsync is installed
          Write-Host "Rsync installed successfully"
          & rsync --version
        shell: powershell

      - name: Deploy site to VPS
        run: |
          rsync -avz --delete one.whispr-site/.next one.whispr-site/package.json one.whispr-site/package-lock.json one.whispr-site/public one.whispr-site/next.config.ts one.whispr-site/scripts one.whispr-site/src/migrations ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${{ secrets.DEPLOY_PATH }}
        shell: bash

      - name: Deploy utility scripts to VPS
        run: |
          scp .github/scripts/rollback.sh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${{ secrets.DEPLOY_PATH }}/scripts/
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "chmod +x ${{ secrets.DEPLOY_PATH }}/scripts/rollback.sh"
        shell: bash
          
      - name: Restart site application
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          timeout: 30s
          script: |
            cd ${{ secrets.DEPLOY_PATH }}
            echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}" > .env.production
            echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> .env.production
            echo "NEXT_PUBLIC_SITE_URL=${{ secrets.NEXT_PUBLIC_SITE_URL }}" >> .env.production
            echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> .env.production
            npm install --production
            npx tsx scripts/apply-migrations.ts
            pm2 restart one-whispr-site || pm2 start npm --name "one-whispr-site" -- start

  # Build and deploy app on self-hosted Windows runner
  build-and-deploy-app:
    runs-on: self-hosted
    if: (github.event.inputs.build_electron == 'true' || contains(github.event.head_commit.message, '[build-electron]')) && github.event.inputs.rollback_to_version == '' && github.event.inputs.emergency_rollback != 'true' && !contains(github.event.head_commit.message, '[skip]')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies for main app
        run: |
          cd one.whispr-app
          npm ci

      - name: Check for Python file changes
        id: python-changes
        run: |
          $MAIN_WHISPR_CHANGED = $false
          $DEPS_CHANGED = $false

          # Check if main.py or whispr folder changed (quick update)
          $mainWhisprFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-app/python/main\.py$|^one\.whispr-app/python/whispr/'
          if ($mainWhisprFiles) {
            $MAIN_WHISPR_CHANGED = $true
            "main-whispr-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Main.py or whispr folder changed - quick update needed"
          } else {
            "main-whispr-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }

          # Check if dependencies or spec files changed (full rebuild)
          $depsFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-app/python/.*\.spec$|^one\.whispr-app/python/utils/python-setup\.ts$|^one\.whispr-app/python/requirements\.txt$|^one\.whispr-app/package\.json$'
          if ($depsFiles) {
            $DEPS_CHANGED = $true
            "deps-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Dependencies or build configuration changed - full rebuild needed"
          } else {
            "deps-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }

          # Set overall python-changed flag
          if ($MAIN_WHISPR_CHANGED -or $DEPS_CHANGED) {
            "python-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Python files have changed, will rebuild"
          } else {
            "python-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "No Python files changed, skipping Python build"
          }
        shell: powershell

      - name: Setup Python environment
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          cd one.whispr-app
          npm run backend-setup

      - name: Install dependencies
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          cd one.whispr-app
          npm install
        shell: powershell

      - name: Compile Python backend (Full rebuild)
        if: steps.python-changes.outputs.deps-changed == 'true'
        run: |
          cd one.whispr-app
          npm run backend-compile

      - name: Quick update (bytecode compilation only)
        if: steps.python-changes.outputs.main-whispr-changed == 'true' && steps.python-changes.outputs.deps-changed == 'false'
        run: |
          cd one.whispr-app
          npm run backend-compile:quick

      - name: Build main Electron app to individual files
        run: |
          cd one.whispr-app
          npm run build:files

      - name: Install dependencies for setup
        run: |
          cd one.whispr-setup
          npm ci

      - name: Remove Scripts.7z from main app (not needed in Microsoft Store)
        run: |
          cd one.whispr-app
          Remove-Item ".release/win-unpacked/resources/backend/OneWhispr-Scripts.7z" -Force -ErrorAction SilentlyContinue
        shell: powershell

      - name: Build setup/installer (Microsoft Store)
        run: |
          cd one.whispr-setup
          npm run build:files:microsoft
        env:
          IS_MICROSOFT: true
          UPDATE_SERVER_URL: https://whispr.one/updates

      - name: Remove remaining 7z files from main app for direct distribution
        run: |
          cd one.whispr-app
          Remove-Item ".release/win-unpacked/resources/backend/OneWhispr-Runtime-Base.7z" -Force -ErrorAction SilentlyContinue
          Remove-Item ".release/win-unpacked/resources/backend/msstore.json" -Force -ErrorAction SilentlyContinue
        shell: powershell

      - name: Build setup/installer (Direct distribution)
        run: |
          cd one.whispr-setup
          npm run build:files:direct
        env:
          UPDATE_SERVER_URL: https://whispr.one/updates

      - name: Get version for deployment
        id: get-version
        run: |
          cd one.whispr-setup
          $VERSION = node -p "require('./package.json').version"
          "version=$VERSION" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          Write-Host "Version: $VERSION"
        shell: powershell

      # Use SCP instead of rsync for Windows compatibility
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Install rsync on Windows
        run: |
          choco install rsync -y
          # Verify rsync is installed
          Write-Host "Rsync installed successfully"
          & rsync --version
        shell: powershell

      - name: Prepare deployment structure
        run: |
          $VERSION = "${{ steps.get-version.outputs.version }}"
          $PYTHON_CHANGED = "${{ steps.python-changes.outputs.python-changed }}"
          $MAIN_WHISPR_CHANGED = "${{ steps.python-changes.outputs.main-whispr-changed }}"
          $DEPS_CHANGED = "${{ steps.python-changes.outputs.deps-changed }}"

          Write-Host "Deploying version: $VERSION"

          # Create directory structure for setup installer
          New-Item -ItemType Directory -Path "updates/setup/versions/$VERSION" -Force
          New-Item -ItemType Directory -Path "updates/setup/latest" -Force

          # Copy setup files
          if (Test-Path "one.whispr-setup/.release-direct") {
            Get-ChildItem -Path "one.whispr-setup/.release-direct" -Recurse -Include "*.exe", "*.nupkg", "RELEASES" | ForEach-Object {
              Copy-Item -Path $_.FullName -Destination "updates/setup/versions/$VERSION/" -Force
              Copy-Item -Path $_.FullName -Destination "updates/setup/latest/" -Force
            }
            Write-Host "Installer files copied"
          }

          # Handle Python artifacts based on what changed
          if ($DEPS_CHANGED -eq "true") {
            Write-Host "Full rebuild - deploying base runtime and scripts"
            New-Item -ItemType Directory -Path "updates/backend-runtime/versions/$VERSION" -Force
            New-Item -ItemType Directory -Path "updates/backend-runtime/latest" -Force
            New-Item -ItemType Directory -Path "updates/backend-scripts/versions/$VERSION" -Force
            New-Item -ItemType Directory -Path "updates/backend-scripts/latest" -Force

            if (Test-Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z") {
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z" -Destination "updates/backend-runtime/versions/$VERSION/" -Force
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z" -Destination "updates/backend-runtime/latest/" -Force
            }
            
            if (Test-Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z") {
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" -Destination "updates/backend-scripts/versions/$VERSION/" -Force
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" -Destination "updates/backend-scripts/latest/" -Force
            }

            New-Item -ItemType File -Path "updates/RUNTIME_DEPLOYED" -Force
            New-Item -ItemType File -Path "updates/SCRIPTS_DEPLOYED" -Force
          } elseif ($MAIN_WHISPR_CHANGED -eq "true") {
            Write-Host "Quick update - deploying only scripts"
            New-Item -ItemType Directory -Path "updates/backend-scripts/versions/$VERSION" -Force
            New-Item -ItemType Directory -Path "updates/backend-scripts/latest" -Force

            if (Test-Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z") {
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" -Destination "updates/backend-scripts/versions/$VERSION/" -Force
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" -Destination "updates/backend-scripts/latest/" -Force
            }

            New-Item -ItemType File -Path "updates/SCRIPTS_DEPLOYED" -Force
          }

          # Always deploy main app
          New-Item -ItemType Directory -Path "updates/main-app/versions/$VERSION" -Force
          New-Item -ItemType Directory -Path "updates/main-app/latest" -Force
          
          if (Test-Path "one.whispr-app/.release/win-unpacked") {
            Copy-Item -Path "one.whispr-app/.release/win-unpacked/*" -Destination "updates/main-app/versions/$VERSION/" -Recurse -Force
            Copy-Item -Path "one.whispr-app/.release/win-unpacked/*" -Destination "updates/main-app/latest/" -Recurse -Force
          }
        shell: powershell

      - name: Create JSON manifest
        run: |
          $VERSION = "${{ steps.get-version.outputs.version }}"
          
          # Create JSON manifest for the setup app
          Set-Location "updates/main-app/versions/$VERSION"
          
          $files = @()
          $totalSize = 0
          
          Get-ChildItem -Path . -Recurse -File | ForEach-Object {
            $relativePath = $_.FullName.Replace((Get-Location).Path, "").TrimStart('\').Replace('\', '/')
            $size = $_.Length
            $checksum = (Get-FileHash -Path $_.FullName -Algorithm SHA256).Hash.ToLower()
            $baseUrl = "https://whispr.one/updates/main-app/latest"
            
            $files += @{
              path = "./$relativePath"
              size = $size
              checksum = $checksum
              url = "$baseUrl/$relativePath"
            }
            
            $totalSize += $size
          }
          
          $manifest = @{
            version = $VERSION
            files = $files
            totalSize = $totalSize
          }
          
          $manifest | ConvertTo-Json -Depth 3 | Out-File -FilePath "../../../manifest-$VERSION.json" -Encoding UTF8
          
          Set-Location ../../../..
          Copy-Item -Path "updates/manifest-$VERSION.json" -Destination "updates/main-app/latest/manifest.json" -Force
        shell: powershell

      - name: Deploy to VPS using rsync
        run: |
          # Create updates directory on VPS
          ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "mkdir -p /var/www/html/updates"
          
          # Use rsync to transfer files (more reliable than SCP)
          rsync -avz --delete-after -e "ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no" updates/ ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/var/www/html/updates/
        shell: bash

      - name: Update version info and cleanup on VPS
        run: |
          ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'ENDSSH'
            cd /var/www/html/updates
            
            # Extract version from uploaded files
            VERSION=$(ls setup/versions/ | sort -V | tail -1)
            
            # Create version.json for setup installer
            cat > setup/version.json << EOF
          {
            "version": "$VERSION",
            "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "downloadUrl": "https://whispr.one/updates/setup/latest/OneWhisprSetup.exe",
            "releaseNotes": "Latest version of OneWhispr Setup",
            "deltaUpdatesUrl": "https://whispr.one/updates/setup/versions/$VERSION/"
          }
          EOF
            
            # Create latest.yml for electron-updater
            if [ -f "setup/latest/OneWhisprSetup.exe" ]; then
              SETUP_FILE_SIZE=$(stat -c%s setup/latest/OneWhisprSetup.exe)
              SETUP_SHA512=$(sha512sum setup/latest/OneWhisprSetup.exe | cut -d' ' -f1)
              cat > setup/latest.yml << EOF
          version: $VERSION
          files:
            - url: OneWhisprSetup.exe
              sha512: $SETUP_SHA512
              size: $SETUP_FILE_SIZE
          path: OneWhisprSetup.exe
          sha512: $SETUP_SHA512
          releaseDate: $(date -u +%Y-%m-%dT%H:%M:%SZ)
          EOF
            fi
            
            # Create version.json for main app updates
            cat > main-app/version.json << EOF
          {
            "version": "$VERSION",
            "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "releaseNotes": "Latest version of One Whispr",
            "downloadUrl": "https://whispr.one/updates/main-app/latest/",
            "manifestUrl": "https://whispr.one/updates/main-app/latest/manifest.json",
            "versionsUrl": "https://whispr.one/updates/main-app/versions/$VERSION/"
          }
          EOF
            
            # Create version files for backend components if they were deployed
            if [ -f "RUNTIME_DEPLOYED" ]; then
              echo "Creating backend runtime version file..."
              if [ -f "backend-runtime/latest/OneWhispr-Runtime-Base.7z" ]; then
                RUNTIME_CHECKSUM=$(sha256sum backend-runtime/latest/OneWhispr-Runtime-Base.7z | cut -d' ' -f1)
                cat > backend-runtime/runtime-version.json << EOF
          {
            "version": "$VERSION",
            "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "releaseNotes": "Backend runtime base package",
            "downloadUrl": "https://whispr.one/updates/backend-runtime/latest/OneWhispr-Runtime-Base.7z",
            "versionsUrl": "https://whispr.one/updates/backend-runtime/versions/$VERSION/",
            "compressionType": "7z-lzma2-ultra",
            "checksum": "$RUNTIME_CHECKSUM"
          }
          EOF
              fi
              rm -f "RUNTIME_DEPLOYED"
            fi
            
            if [ -f "SCRIPTS_DEPLOYED" ]; then
              echo "Creating backend scripts version file..."
              if [ -f "backend-scripts/latest/OneWhispr-Scripts.7z" ]; then
                SCRIPTS_CHECKSUM=$(sha256sum backend-scripts/latest/OneWhispr-Scripts.7z | cut -d' ' -f1)
                cat > backend-scripts/scripts-version.json << EOF
          {
            "version": "$VERSION",
            "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "releaseNotes": "Backend scripts bytecode update",
            "downloadUrl": "https://whispr.one/updates/backend-scripts/latest/OneWhispr-Scripts.7z",
            "versionsUrl": "https://whispr.one/updates/backend-scripts/versions/$VERSION/",
            "updateType": "bytecode",
            "compressionType": "7z",
            "checksum": "$SCRIPTS_CHECKSUM"
          }
          EOF
              fi
              rm -f "SCRIPTS_DEPLOYED"
            fi
            
            # Keep only last 5 versions (cleanup)
            cd setup/versions && ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            cd ../../main-app/versions && ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            
            if [ -d "../../backend-runtime/versions" ]; then
              cd ../../backend-runtime/versions && ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            fi
            
            if [ -d "../../backend-scripts/versions" ]; then
              cd ../../backend-scripts/versions && ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            fi
            
            echo "Deployment completed successfully!"
          ENDSSH
        shell: bash

  # Rollback functionality
  rollback:
    runs-on: self-hosted
    if: github.event.inputs.rollback_to_version != '' || github.event.inputs.emergency_rollback == 'true'

    steps:
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Execute rollback
        run: |
          if [ "${{ github.event.inputs.emergency_rollback }}" = "true" ]; then
            echo "Performing emergency rollback to previous version..."
            ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd ${{ secrets.DEPLOY_PATH }}/scripts && ./rollback.sh --emergency"
          else
            echo "Rolling back to version ${{ github.event.inputs.rollback_to_version }}..."
            ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd ${{ secrets.DEPLOY_PATH }}/scripts && ./rollback.sh --version ${{ github.event.inputs.rollback_to_version }}"
          fi
        shell: bash