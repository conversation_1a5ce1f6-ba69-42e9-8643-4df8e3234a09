# OneWhispr Electron Deployment Guide

## 🚀 Overview

This document describes the complete deployment and update system for the OneWhispr Electron application, implementing a **Discord-style installer/updater/launcher** with **individual file downloads** for maximum efficiency.

## 📋 Table of Contents

- [Architecture](#-architecture)
- [Quick Start](#-quick-start)
- [Deployment Process](#-deployment-process)
- [Rollback Process](#-rollback-process)
- [File Structure](#-file-structure)
- [Update System](#-update-system)
- [Bandwidth Optimization](#-bandwidth-optimization)
- [Troubleshooting](#-troubleshooting)

## 🏗️ Architecture

### 3-in-1 System
Our setup creates a **small installer** that serves three purposes:

1. **🚀 Installer**: Downloads and installs the main app using individual files
2. **🔄 Updater**: Checks for updates and downloads only changed files  
3. **🎯 Launcher**: Always runs first, then launches the actual app

### 🎯 **Key Benefits - Discord-Style Updates**
- **90% Bandwidth Savings**: Updates download only changed files (10-50MB vs 1GB+)
- **Progress Tracking**: Individual file downloads with detailed progress bars
- **Instant Rollbacks**: Previous versions available on server
- **Small Initial Download**: Setup.exe is only 50-100MB, then downloads main app

### User Experience Flow
```
User downloads OneWhisprSetup.exe (50-100MB)
           ↓
OneWhisprSetup.exe launches → Checks for updates
           ↓
Downloads main app individual files (1GB+ first time only)
           ↓
Future updates: Downloads only changed files (10-50MB)
           ↓ 
Launches OneWhispr main application
```

### File Structure on VPS
```
whispr.one/public/updates/
├── setup/                         # Setup installer updates
│   ├── version.json              # Setup version info
│   ├── latest/                   # Current setup version
│   │   ├── OneWhisprSetup.exe    # What users download
│   │   ├── one.whispr-app-<ver>.nupkg
│   │   └── RELEASES
│   └── versions/                 # Setup version history (last 5)
│       ├── 1.2.3/
│       │   ├── OneWhisprSetup.exe
│       │   ├── one.whispr-app-1.2.3.nupkg
│       │   └── RELEASES
│       └── ...
├── main-app/                      # Main app individual files
│   ├── version.json              # Main app version info
│   ├── latest/                   # Current main app version
│   │   ├── One Whispr.exe        # Main executable
│   │   ├── resources/            # App resources
│   │   ├── locales/              # Localization files
│   │   └── manifest.json         # File list with checksums
│   └── versions/                 # Main app version history (last 5)
│       ├── 1.2.3/               # Latest version
│       │   ├── One Whispr.exe
│       │   ├── resources/
│       │   ├── locales/
│       │   └── [all app files]
│       └── ...
├── python-runtime/                # Python base runtime (NEW)
│   ├── version.json              # Runtime version info
│   ├── latest/                   # Current runtime version
│   │   └── One Whispr Backend-base.7z  # Compressed base (~1.2GB)
│   └── versions/                 # Runtime version history (last 5)
│       ├── 1.2.3/
│       │   └── One Whispr Backend-base.7z
│       └── ...
└── python-quick/                  # Python quick updates (NEW)
    ├── version.json              # Quick update version info
    ├── latest/                   # Current quick update
    │   ├── main.py               # Updated main script
    │   └── whispr/               # Updated whispr module
    └── versions/                 # Quick update history (last 5)
        ├── 1.2.3/
        │   ├── main.py
        │   └── whispr/
        └── ...
```

## 🚀 Quick Start

### Deploy New Version
```bash
# Method 1: Include in commit message (triggers build + deploy)
git commit -m "Add new feature [build-electron]"
git push

# Method 2: Manual trigger (build + deploy)
# Go to GitHub Actions → "Build and Deploy One Whispr" → Run workflow → Check "Build Electron App"

# Method 3: Skip with [skip] in commit message
git commit -m "Update documentation [skip]"
git push  # This will skip all builds
```

### Emergency Rollback
```bash
# Go to GitHub Actions → "Build and Deploy One Whispr" → Run workflow → Check "Emergency rollback"
```

### Specific Version Rollback
```bash
# Go to GitHub Actions → "Build and Deploy One Whispr" → Run workflow → Enter version in "Rollback to specific version"
```

### Force Python Deploy Options
```bash
# Force Python runtime deploy
# Go to GitHub Actions → "Build and Deploy One Whispr" → Run workflow → Check "Force deploy Python artifacts"

# Force scripts only (quick update)
# Go to GitHub Actions → "Build and Deploy One Whispr" → Run workflow → Check "Force deploy scripts only"
```

## 📦 Deployment Process

### Automatic Triggers
- **Website**: Deploys on every push to `main`
- **Electron App**: Deploys when:
  - Commit message contains `[build-electron]`
  - Manual workflow trigger with "Build Electron App" checked

### 🏗️ **Single Optimized Workflow System**
The deployment system uses a single streamlined workflow that eliminates GitHub Actions artifacts:

1. **`build-and-deploy.yaml`** - Complete Build and Deploy (1.5 hours)
   - **Site builds on Ubuntu** (faster for Node.js)
   - **App builds on Windows** (required for Electron)
   - **Direct SSH transfers** - no GitHub storage usage
   - Handles rollbacks and version management
   - Uses SCP instead of rsync for Windows compatibility

### 🎯 **Key Benefits**
- **Zero GitHub Storage**: No artifacts quota usage
- **Direct Deployment**: Files transfer immediately after building
- **Faster Deployment**: No upload/download artifact steps
- **Windows Compatible**: Uses SCP for reliable transfers
- **Single File Management**: Easier to maintain than 3 separate workflows

### Build Steps
1. **Build Main App to Individual Files** (`one.whispr-app`)
   - Compiles TypeScript → `.release/`
   - Bundles with Vite
   - Creates individual files using `electron-builder --target=dir`
   - Outputs to `.release/win-unpacked/`
   - Generates file manifest with checksums for efficient updates

2. **Build Setup Installer** (`one.whispr-setup`)
   - Creates installer/updater/launcher
   - Small executable that manages main app files
   - Handles file downloading with progress bars
   - Outputs to `.release/squirrel-windows/` (contains `OneWhisprSetup.exe`, `.nupkg`, `RELEASES`)
   - **Installer filename is always `OneWhisprSetup.exe`** (set via `artifactName` in `electron-builder.json`)

3. **Deploy to VPS with File-Based Updates**
   - **Setup Channel**: `/updates/setup/` - For installer updates
   - **Main App Channel**: `/updates/main-app/` - For individual app files
   - Creates version-specific directories for both channels
   - Creates manifest.json with file checksums
   - Updates both `version.json` files
   - Cleans up old versions (keeps last 5 of each)

### Version Management
- **Storage**: Last 5 versions kept automatically for both channels
- **Individual File Updates**: Only changed files downloaded (10-50MB vs 1GB+)
- **Bandwidth Savings**: ~90% reduction for existing users
- **Dual Channel**: Setup and main app can update independently

### Python Backend Build System (OPTIMIZED - Separated Build)

Our Python backend uses an **optimized separated build system** with **aggressive package exclusions** for maximum efficiency:

#### 🎯 **Optimized Build Architecture**
- **Base Runtime**: Essential dependencies only (torch, transformers, etc.) compressed with 7zip (~1.3GB)
- **Updatable Bytecode**: Compiled `main.pyc` and `whispr/` modules (~200KB)
- **Smart Detection**: Automatically detects what type of Python changes occurred
- **Package Exclusions**: 120+ unnecessary packages excluded (pandas, matplotlib, sklearn, etc.)

#### 📦 **Build Modes**
1. **Full Rebuild** (when dependencies change):
   ```bash
   npm run backend-compile
   ```
   - Creates optimized base runtime with only essential dependencies
   - Excludes 120+ unnecessary packages (pandas, matplotlib, sklearn, scipy.*, etc.)
   - Compresses with 7zip (65% reduction: ~3.8GB → ~1.3GB)
   - Compiles Python code to bytecode for protection

2. **Quick Update** (when only Python code changes):
   - Compiles Python code to bytecode (.pyc files)
   - Creates compressed archive with `main.pyc` and `whispr/` bytecode
   - Typical size: ~200KB vs 1.3GB base runtime

#### 🔍 **Automatic Change Detection**
The workflow automatically detects:
- **Dependencies Changed**: `.spec` files, `utils/`, `package.json` → Full rebuild
- **Code Changed**: `main.py` or `whispr/` folder → Quick update only
- **No Python Changes**: Skips Python build entirely

#### 📁 **Update Channels**
```
whispr.one/updates/
├── setup/                       # Setup installer updates
│   ├── version.json
│   ├── latest/
│   │   └── OneWhisprSetup.exe
│   └── versions/
├── main-app/                    # Main application files
│   ├── version.json
│   ├── latest/
│   │   ├── manifest.json        # File checksums and URLs
│   │   ├── One Whispr.exe
│   │   └── resources/
│   └── versions/
├── python-runtime/              # Base runtime (~1.3GB, downloaded once)
│   ├── version.json             # Now includes checksum field
│   ├── latest/
│   │   └── OneWhispr-Runtime-Base.7z
│   └── versions/
└── python-updatable/            # Bytecode updates (~200KB)
    ├── version.json             # Now includes checksum field
    ├── latest/
    │   └── OneWhispr-Updatable.7z  # Contains main.pyc + whispr/ bytecode
    └── versions/
```

#### 💡 **Benefits**
- **65% Size Reduction**: 1.3GB vs 3.8GB+ for base runtime
- **Lightning Updates**: Python code changes = ~200KB downloads
- **Source Protection**: Bytecode compilation prevents code inspection
- **Package Optimization**: 120+ unnecessary packages excluded
- **NPM-Managed Tools**: 7zip as dev dependency (no system installs)
- **Smart CI/CD**: Only rebuilds what changed

## 🔄 Rollback Process

### When to Rollback
- 🚨 **Emergency**: Critical bug in production
- 🎯 **Selective**: Revert to specific stable version
- 🔧 **Testing**: Roll back to test different versions

### 🔒 **Smart Rollback System**
The rollback system uses intelligent update detection to minimize unnecessary downloads:

#### **Main App & Setup**: `releaseDate` + `isRollback` flag
- Uses timestamp comparison for normal updates
- `isRollback: true` forces download regardless of date
- Efficient for all file-based components

#### **Python Runtime**: Checksum-based rollback detection
- Compares SHA256 checksums to detect actual changes
- **Prevents unnecessary 1.2GB downloads** during rollbacks
- Only downloads if runtime files actually changed
- Saves bandwidth and user time

```json
// Example: Python runtime rollback with same files
{
  "version": "1.0.0",
  "releaseDate": "2025-06-21T10:00:00Z",
  "checksum": "abc123...",  // Same as current = no download
  "isRollback": true
}
```

### Rollback Methods

#### Method 1: GitHub Actions (Recommended)
**Emergency Rollback:**
1. Go to GitHub Actions
2. Click "Build and Deploy One Whispr" workflow
3. Click "Run workflow"
4. Check ☑️ "Emergency rollback to previous version"
5. Click "Run workflow"
6. ✅ Complete in ~2 minutes

**Specific Version Rollback:**
1. Go to GitHub Actions
2. Click "Build and Deploy One Whispr" workflow
3. Click "Run workflow"
4. Enter version (e.g., `1.2.1`) in "Rollback to specific version"
5. Click "Run workflow"
6. ✅ Complete in ~2 minutes

#### Method 2: Manual SSH (Emergency)
```bash
# SSH to VPS
ssh <EMAIL>
cd /your/deploy/path/scripts

# Emergency rollback
./rollback.sh emergency

# Specific version rollback  
./rollback.sh 1.2.1
```

### Rollback Timeline
```
T+0:     Rollback triggered
T+30s:   Server files updated  
T+1min:  New downloads get rollback version
T+5min:  Existing users receive rollback by downloading changed files (10-50MB)
```

## 🗂️ File Structure

### Project Structure
```
one.whispr/
├── one.whispr-app/              # Main Electron application
│   ├── src/                     # App source code
│   ├── package.json            # Includes electron-builder with dir target
│   └── .release/win-unpacked/  # Built app files (individual files)
├── one.whispr-setup/           # Installer/Updater/Launcher  
│   ├── src/                    # Setup logic for file management
│   ├── package.json           # Setup + build dependencies
│   └── .release/squirrel-windows/ # Built installer and update files
└── .github/
    └── workflows/
        └── build-and-deploy.yaml  # Single optimized workflow
```

### 🔧 **Workflow Configuration**

**build-and-deploy.yaml** (Complete Workflow):
```yaml
# Single workflow handling all build and deployment
# No artifacts - direct SSH transfers
# Split jobs for optimal performance
on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      build_electron: # Build Electron App
      force_python_deploy: # Deploy runtime + scripts
      force_scripts_only: # Deploy scripts only
      rollback_to_version: # Rollback to specific version
      emergency_rollback: # Emergency rollback

jobs:
  build-site:
    runs-on: ubuntu-latest  # Fast Node.js builds
    # Builds and deploys site immediately via rsync
    
  build-and-deploy-app:
    runs-on: windows-latest  # Required for Electron
    # Builds app and deploys immediately via SCP
    
  rollback:
    runs-on: ubuntu-latest  # Handles rollback operations
```

### Dependencies Configuration

**one.whispr-app/package.json** (UPDATED):
```json
{
  "scripts": {
    "build:squirrel": "npm run build && electron-builder --win"
  },
  "build": {
    "appId": "one.whispr-app",
    "productName": "One Whispr",
    "directories": {
      "output": ".release"
    },
    "win": {
      "target": [{"target": "dir", "arch": ["x64"]}],
      "icon": "src/assets/icon.ico"
    }
  },
  "devDependencies": {
    "electron": "^35.1.5",
    "electron-builder": "^26.0.12"
  }
}
```

**one.whispr-setup/package.json** (UPDATED):
```json
{
  "build": {
    "appId": "one.whispr-app",
    "productName": "One Whispr Setup",
    "directories": {
      "output": ".release"
    },
    "win": {
      "target": [{"target": "squirrel", "arch": ["x64"]}],
      "icon": "src/assets/icon.ico",
      "artifactName": "OneWhisprSetup.${ext}"
    },
    "squirrelWindows": {
      "iconUrl": "https://whispr.one/icon.ico",
      "useAppIdAsId": true
    },
    "publish": {
      "provider": "generic",
      "url": "https://whispr.one/updates/setup"
    }
  },
  "devDependencies": {
    "electron": "^35.1.5",
    "electron-builder": "^26.0.12"
  }
}
```
> **Note:** The `artifactName` property in `electron-builder.json` ensures the installer is always named `OneWhisprSetup.exe` (no spaces, no version).

## 🔄 Update System

### How Updates Work

#### For New Users (First Install)
```
1. Download OneWhisprSetup.exe (50-100MB)
2. Setup downloads all main app files (1GB+ - unavoidable first time)
3. Desktop shortcut points to OneWhisprSetup.exe (not main app)
```

#### For Existing Users (Updates)
```
1. User clicks OneWhisprSetup shortcut → Launches OneWhisprSetup.exe
2. Setup checks https://whispr.one/updates/setup/version.json for self-updates
3. Setup checks https://whispr.one/updates/main-app/version.json for main app updates
4. Downloads manifest.json to compare file checksums
5. Downloads ONLY changed files (10-50MB typical)
6. Setup launches updated main app
```

## 🎯 Bandwidth Optimization

### Update Size Comparison
| Update Type | Before (Monolithic) | After (Optimized) | Savings |
|-------------|---------------------|-------------------|---------|
| **First Install** | 4GB+ | 1.3GB base + 1GB app | **65% savings** |
| **Python Code Update** | 4GB+ | ~200KB | **99.95% savings** |
| **App Update** | 4GB+ | 10-50MB | **98% savings** |
| **Python Deps Update** | 4GB+ | 1.3GB runtime | **65% savings** |
| **Hotfix** | 4GB+ | ~200KB | **99.95% savings** |
| **Rollback** | 4GB+ | Instant (cached) | **100% savings** |

### Python Backend Optimization
| Change Type | Old System | New Optimized System | Savings |
|-------------|------------|---------------------|---------|
| **Fix Python bug** | 3.8GB+ rebuild | ~200KB bytecode update | **99.995% savings** |
| **Update ML model** | 3.8GB+ rebuild | ~200KB bytecode update | **99.995% savings** |
| **Add Python feature** | 3.8GB+ rebuild | ~200KB bytecode update | **99.995% savings** |
| **Update dependencies** | 3.8GB+ rebuild | 1.3GB base runtime | **65% savings** |

### Why This Matters for Large Apps
- **Before**: Every user downloads 4GB+ for any change
- **After**: Users download only what changed
  - Python code changes: ~200KB
  - App changes: 10-50MB
  - Dependency changes: 1.3GB (rare)
- **Real Impact**:
  - 1000 users × 4GB = 4TB bandwidth per update
  - 1000 users × 200KB = 200MB bandwidth per Python update
  - **99.995% bandwidth reduction = 20,000x cost savings for Python updates**

## ✅ **Implementation Complete**

### Checksum Functionality
The deployment scripts now include checksums in backend version.json files:

```bash
# In deploy.yaml - Update version info step
# Checksum calculation for backend components:

# For backend runtime
if [ -d "backend-runtime/latest" ]; then
  RUNTIME_CHECKSUM=$(sha256sum backend-runtime/latest/OneWhispr-Runtime-Base.7z | cut -d' ' -f1)
  cat > backend-runtime/runtime-version.json << EOF
{
  "version": "$VERSION",
  "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "releaseNotes": "Backend runtime base package",
  "downloadUrl": "https://whispr.one/updates/backend-runtime/latest/OneWhispr-Runtime-Base.7z",
  "versionsUrl": "https://whispr.one/updates/backend-runtime/versions/$VERSION/",
  "compressionType": "7z-lzma2-ultra",
  "checksum": "$RUNTIME_CHECKSUM"
}
EOF
fi

# For backend scripts
if [ -d "backend-scripts/latest" ]; then
  SCRIPTS_CHECKSUM=$(sha256sum backend-scripts/latest/OneWhispr-Scripts.7z | cut -d' ' -f1)
  cat > backend-scripts/scripts-version.json << EOF
{
  "version": "$VERSION",
  "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "releaseNotes": "Backend scripts bytecode update",
  "downloadUrl": "https://whispr.one/updates/backend-scripts/latest/OneWhispr-Scripts.7z",
  "versionsUrl": "https://whispr.one/updates/backend-scripts/versions/$VERSION/",
  "updateType": "bytecode",
  "compressionType": "7z",
  "checksum": "$SCRIPTS_CHECKSUM"
}
EOF
fi
```

**Status**: ✅ Client-side checksum logic implemented, ✅ Server-side checksum generation implemented

## 🔧 Troubleshooting

### Common Issues

#### Build Fails with "icon must be at least 256x256"
```bash
# Remove icon temporarily or add proper 256x256 .ico file
# In package.json, remove:
"icon": "assets/favicon.ico"
```

#### Build Hangs on Code Signing
```bash
# Disable signing for development:
"win": {
  "signAndEditExecutable": false
}
```

#### Delta Updates Not Working
1. Check `RELEASES` file exists in version directory
2. Verify `electron-updater` dependency in main app
3. Ensure `publish.url` points to correct update channel

### Debugging Commands
```bash
# Check deployed versions
curl https://whispr.one/updates/setup/version.json
curl https://whispr.one/updates/main-app/version.json

# Check manifest with file checksums
curl https://whispr.one/updates/main-app/latest/manifest.json

# List available versions  
curl https://whispr.one/updates/setup/versions/
curl https://whispr.one/updates/main-app/versions/

# Check build artifacts locally
ls one.whispr-setup/.release/squirrel-windows/
ls one.whispr-app/.release/win-unpacked/
```

### Manual VPS Commands
```bash
# SSH to VPS
ssh <EMAIL>
cd /path/to/deploy/public/updates

# Check current structure
tree . -L 3

# Manual version check
cat setup/version.json
cat main-app/version.json

# Manual cleanup old versions (keeps last 5)
cd setup/versions && ls -1 | sort -V | head -n -5 | xargs rm -rf
cd ../../main-app/versions && ls -1 | sort -V | head -n -5 | xargs rm -rf
```

---

## 🎉 Success Metrics

When properly deployed, you should see:
- ✅ **Initial setup download**: 50-100MB (not 1GB+)
- ✅ **Update downloads**: 10-50MB average (not 1GB+)
- ✅ **95% bandwidth savings** for existing users
- ✅ **Instant rollbacks** without re-downloading
- ✅ **Atomic updates** - no corruption possible
- ✅ **Dual update channels** - setup and app can update independently

Your Discord-style updater is now production-ready for large applications! 🚀 