/**
 * ModelPage Component
 * Complete model selection page matching app-latest ModelStep functionality
 * Integrates ModelContext with sophisticated model operations and auto-advance
 */

import React from 'react';
import { useModel } from '../contexts/ModelContext';

import { ModelSelector } from '../components/model/ModelSelector';
import type { VoiceModel } from '../types/model';

interface ModelPageProps {
  onNext?: () => void;
  onPrevious?: () => void;
}

export function ModelPage({ onNext, onPrevious }: ModelPageProps) {
  const { 
    state,
    loadModels,
    selectModel,
    downloadModel,
    loadModel,
    cancelDownload,
    clearError,
    isModelReady,
    isUserInitiatedLoading
  } = useModel();

  const { 
    models, 
    selectedModel, 
    downloadProgress,
    loadingProgress,
    loading, 
    error 
  } = state;

  // Auto-advance when model is loaded and ready (only for user-initiated loading)
  React.useEffect(() => {
    if (isModelReady && selectedModel?.isLoaded && isUserInitiatedLoading && onNext) {
      console.log(`Model ${selectedModel.id} loaded successfully (user-initiated), auto-proceeding to next step`);
      // Add a small delay to show success state
      const timer = setTimeout(() => {
        onNext();
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [isModelReady, selectedModel?.isLoaded, isUserInitiatedLoading, onNext]);

  const handleModelSelect = (model: VoiceModel) => {
    selectModel(model);
  };

  const handleModelDownload = async (model: VoiceModel) => {
    try {
      await downloadModel(model);
    } catch (error) {
      console.error('Failed to download model:', error);
    }
  };

  const handleModelLoad = async (model: VoiceModel) => {
    try {
      // User-initiated loading (will trigger auto-advance)
      await loadModel(model, true);
    } catch (error) {
      console.error('Failed to load model:', error);
    }
  };

  const handleCancelDownload = async () => {
    try {
      await cancelDownload();
    } catch (error) {
      console.error('Failed to cancel download:', error);
    }
  };

  const handleRefresh = async () => {
    try {
      await loadModels();
    } catch (error) {
      console.error('Failed to refresh models:', error);
    }
  };

  return (
    <div className="h-full flex items-center justify-center px-6">
      <div className="max-w-2xl w-full space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-semibold text-foreground">Choose Your Voice Model</h2>
          <p className="text-muted-foreground">
            Select a speech recognition model based on your needs and hardware capabilities
          </p>
        </div>

        {/* Model Selection with full functionality */}
        <ModelSelector
          models={models}
          selectedModel={selectedModel}
          onModelSelect={handleModelSelect}
          downloadProgress={downloadProgress}
          loadingProgress={loadingProgress}
          loading={loading}
          error={error}
          onDownload={handleModelDownload}
          onLoad={handleModelLoad}
          onCancel={handleCancelDownload}
          onRefresh={handleRefresh}
          onNext={onNext}
          onPrevious={onPrevious}
        />
      </div>
    </div>
  );
} 