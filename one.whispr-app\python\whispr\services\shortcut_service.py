"""
Shortcut service for managing keyboard shortcuts.

This module contains the ShortcutService class which manages keyboard shortcuts
for starting, stopping, and canceling recording, as well as switching modes.
"""

import logging
import time
import threading
import queue
import asyncio
from typing import Dict, Any, Set, List, Optional, Callable, Tuple

from ..core.base import BaseService, Event
from ..helpers.shortcut.shortcut_recorder import ShortcutRecorder
from ..helpers.shortcut.shortcut_keyboard_manager import ShortcutKeyboardManager
from ..config.manager import ConfigurationMixin

logger = logging.getLogger('whispr.shortcut')

class ShortcutService(BaseService, ConfigurationMixin):
    """Service for managing keyboard shortcuts."""
    
    def __init__(self, service_container=None):
        """Initialize the shortcut service.
        
        Args:
            service_container: The service container instance
        """
        super().__init__(service_container)
        
        # For tracking state
        self.recording_shortcuts = False
        self.is_recording = False
        
        # Simple test mode flag
        self.is_test_mode_active = False
        
        # Create helpers
        self.keyboard_manager = ShortcutKeyboardManager(service_container)
        self.shortcut_recorder = ShortcutRecorder(service_container)
        
        # Thread control flags
        self._stop_thread = threading.Event()
        self.detection_thread = None
        self.action_thread = None
        
        # Message queue for cross-thread communication
        self.action_queue = queue.Queue()
    
    async def initialize(self) -> bool:
        """Initialize the service.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        self.logger.info("Initializing ShortcutService")
        
        # Configure keyboard manager with current settings (no caching)
        self._configure_keyboard_manager()
        
        # Set up callbacks for the keyboard manager
        self.keyboard_manager.on_start_recording = self._queue_start_recording
        self.keyboard_manager.on_stop_recording = self._queue_stop_recording
        self.keyboard_manager.on_cancel_recording = self._queue_cancel_recording
        self.keyboard_manager.on_mode_switch = self._queue_mode_switch
        self.keyboard_manager.on_window_cancelled = self._notify_window_cancelled
        
        # Set up callback for shortcut recorder completion
        self.shortcut_recorder.on_recording_complete = self._on_shortcut_recording_complete
        
        # Using pull-based configuration approach
        self.logger.info("Using pull-based configuration approach")
        
        # Start dedicated shortcut detection thread
        self._stop_thread.clear()
        self.detection_thread = threading.Thread(
            target=self._run_detection_loop,
            name="ShortcutDetector",
            daemon=True
        )
        self.detection_thread.start()
        
        # Start action processing thread
        self.action_thread = threading.Thread(
            target=self._process_action_queue,
            name="ShortcutActionProcessor",
            daemon=True
        )
        self.action_thread.start()
        
        self.logger.info("Shortcut detection threads started")
        
        return await super().initialize()
    
    async def cleanup(self) -> bool:
        """Clean up service resources.
        
        Returns:
            True if cleanup was successful, False otherwise
        """
        self.logger.info("Cleaning up shortcut service resources")
        
        try:
            # Stop any ongoing shortcut recording
            if self.recording_shortcuts:
                self.shortcut_recorder.stop_recording()
                
            # Stop threads
            self._stop_thread.set()
            
            if self.detection_thread and self.detection_thread.is_alive():
                self.detection_thread.join(timeout=1.0)
                
            if self.action_thread and self.action_thread.is_alive():
                self.action_thread.join(timeout=1.0)
                
            # Unhook all keyboard hooks
            self.keyboard_manager.unhook_all_keys()
            
            # Clear resources
            self.is_recording = False
            self.recording_shortcuts = False
            
        except Exception as e:
            self.logger.error(f"Error cleaning up shortcut service: {e}", exc_info=True)
            
        return await super().cleanup()
    
    def _configure_keyboard_manager(self) -> None:
        """Configure keyboard manager with current settings (no caching)."""
        try:
            # Get fresh settings and mode
            settings = self.get_settings()
            
            # Extract shortcuts and recording mode
            shortcuts = settings.get("shortcuts", {})
            recording_mode = settings.get("recordingMode", "pushToTalk")
            
            # Configure keyboard manager with shortcuts
            self.keyboard_manager.set_shortcuts(shortcuts)
            self.shortcut_recorder.set_shortcuts(shortcuts)
            
            # Set recording mode (push-to-talk or toggle)
            self.keyboard_manager.push_to_talk_mode = (recording_mode == 'pushToTalk')
            
            self.logger.info(f"Keyboard manager configured: mode={recording_mode}, shortcuts={len(shortcuts)} defined")
            
        except Exception as e:
            self.logger.error(f"Error configuring keyboard manager: {e}")

    # Detection & Action Processing Threads
    def _run_detection_loop(self):
        """Run the shortcut detection loop in a dedicated thread."""
        self.logger.info("Shortcut detection thread started")
        
        while not self._stop_thread.is_set():
            try:
                # Ensure hooks are setup properly
                if not self.keyboard_manager.hooks_setup:
                    self.keyboard_manager.setup_hooks()
                
                # Most actual detection happens in the keyboard hooks,
                # but this keeps the thread alive and responsive
                
                # Sleep to avoid tight loop
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Error in shortcut detection thread: {e}", exc_info=True)
                time.sleep(1)  # Avoid rapid error loops
    
    def _process_action_queue(self):
        """Process the action queue in a dedicated thread."""
        self.logger.info("Shortcut action processing thread started")
        
        while not self._stop_thread.is_set():
            try:
                # Get action from queue with timeout
                try:
                    action, args = self.action_queue.get(timeout=0.5)
                    
                    # Process the action based on type
                    if action == 'start_recording':
                        shortcut = args[0] if args else None
                        self._perform_start_recording(shortcut)
                    elif action == 'stop_recording':
                        shortcut = args[0] if args else None
                        self._perform_stop_recording(shortcut)
                    elif action == 'cancel_recording':
                        self._perform_cancel_recording()
                    elif action == 'mode_switch':
                        self._perform_mode_switch()
                    
                    # Mark task as done
                    self.action_queue.task_done()
                    
                except queue.Empty:
                    # No actions to process - this is normal
                    pass
                    
            except Exception as e:
                self.logger.error(f"Error processing shortcut action: {e}", exc_info=True)
                time.sleep(0.5)  # Avoid rapid error loops

    # Test Mode Functionality
    def enable_test_mode(self) -> None:
        """Enable test mode for shortcut testing.
        
        In test mode, shortcuts are detected and broadcast as events but don't
        trigger the normal recording/transcription actions.
        """
        if self.is_test_mode_active:
            self.logger.info("Test mode already active")
            return
            
        self.logger.info("Enabling shortcut test mode")
        self.is_test_mode_active = True
        
        # Notify frontend
        if self.service_container:
            ipc_bridge = self.service_container.resolve("ipc")
            if ipc_bridge:
                event_message = {
                    "type": "event",
                    "event": "shortcut_test_mode_enabled",
                    "data": {
                        "test_mode": True,
                        "timestamp": time.time()
                    }
                }
                ipc_bridge.send_message_sync(event_message)
    
    def disable_test_mode(self) -> None:
        """Disable test mode."""
        if not self.is_test_mode_active:
            self.logger.info("Test mode already inactive")
            return
            
        self.logger.info("Disabling shortcut test mode")
        self.is_test_mode_active = False
        
        # Notify frontend
        if self.service_container:
            ipc_bridge = self.service_container.resolve("ipc")
            if ipc_bridge:
                event_message = {
                    "type": "event",
                    "event": "shortcut_test_mode_disabled",
                    "data": {
                        "test_mode": False,
                        "timestamp": time.time()
                    }
                }
                ipc_bridge.send_message_sync(event_message)
    
    # Shortcut Action Queue Methods
    def _queue_start_recording(self, shortcut: str):
        """Queue a start recording action.
        
        Args:
            shortcut: The shortcut that triggered the recording
        """
        # If in test mode, don't queue actual recording - just send event
        if self.is_test_mode_active:
            self.logger.info(f"Test mode: Start recording shortcut detected ({shortcut})")
            self._send_shortcut_detected_event("start_recording", shortcut)
            return
        
        # Normal mode - queue the action
        self.action_queue.put(('start_recording', [shortcut]))
    
    def _queue_stop_recording(self, shortcut: str):
        """Queue a stop recording action.
        
        Args:
            shortcut: The shortcut that triggered the stop
        """
        # If in test mode, don't queue actual recording - just send event
        if self.is_test_mode_active:
            self.logger.info(f"Test mode: Stop recording shortcut detected ({shortcut})")
            self._send_shortcut_detected_event("stop_recording", shortcut)
            return
        
        # Normal mode - queue the action
        self.action_queue.put(('stop_recording', [shortcut]))
    
    def _queue_cancel_recording(self):
        """Queue a cancel recording action."""
        # If in test mode, don't queue actual recording - just send event
        if self.is_test_mode_active:
            self.logger.info("Test mode: Cancel recording shortcut detected")
            self._send_shortcut_detected_event("cancel_recording")
            return
        
        # Normal mode - queue the action
        self.action_queue.put(('cancel_recording', []))
    
    def _queue_mode_switch(self):
        """Queue a mode switch action."""
        # If in test mode, don't queue actual mode switch - just send event
        if self.is_test_mode_active:
            self.logger.info("Test mode: Mode switch shortcut detected")
            self._send_shortcut_detected_event("mode_switch")
            return
        
        # Normal mode - queue the action
        self.action_queue.put(('mode_switch', []))
    
    def _send_shortcut_detected_event(self, action: str, shortcut: str = None):
        """Send shortcut detected event in test mode.
        
        Args:
            action: The action type (start_recording, stop_recording, etc.)
            shortcut: The shortcut that triggered the action (optional)
        """
        if self.service_container:
            ipc_bridge = self.service_container.resolve("ipc")
            if ipc_bridge:
                event_data = {
                    "action": action,
                    "timestamp": time.time()
                }
                
                if shortcut:
                    event_data["shortcut"] = shortcut
                
                event_message = {
                    "type": "event",
                    "event": "shortcut_detected",
                    "data": event_data
                }
                
                ipc_bridge.send_message_sync(event_message)
    
    # Shortcut Action Handlers
    def _perform_start_recording(self, shortcut: str):
        """Perform start recording action.
        
        Args:
            shortcut: The shortcut that triggered the recording
        """
        # In test mode, only send event and don't start actual recording
        if self.is_test_mode_active:
            self.logger.info(f"Test mode: Start recording triggered by {shortcut}")
            self._notify_recording_started(shortcut)
            return
            
        # Skip if already recording
        if self.is_recording:
            return
        
        # Update state
        self.is_recording = True
        self.keyboard_manager.is_recording = True
        
        # Generate session ID
        session_id = f"shortcut_{int(time.time())}"
        
        success = False
        
        # Start AudioService capture with recording
        if self.service_container:
            audio_service = self.service_container.resolve("audio")
            if audio_service:
                # Start capture with recording enabled
                # AudioService will automatically determine system audio from mode configuration
                success = audio_service.start_capture(session_id=session_id)
                
                if success:
                    self.logger.info(f"Audio capture started (session: {session_id})")
                else:
                    self.logger.error("Failed to start audio capture")
            else:
                self.logger.warning("AudioService not available")
        
        # Start TranscriptionService if available and audio service started successfully
        if success and self.service_container:
            transcription_service = self.service_container.resolve("transcription")
            if transcription_service:
                try:
                    # Use asyncio to call the async start method
                    import asyncio
                    
                    # Create a task to start transcription in the background
                    def start_transcription_task():
                        try:
                            # Create new event loop for this thread if needed
                            try:
                                loop = asyncio.get_event_loop()
                            except RuntimeError:
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                            
                            # Determine processing mode from active mode configuration
                            from whispr.helpers.transcription.transcription_engine import ProcessingMode
                            
                            processing_mode = ProcessingMode.BATCH  # Default to BATCH
                            try:
                                active_mode = self.get_active_mode()
                                if active_mode and active_mode.get("configuration"):
                                    real_time_transcription = active_mode["configuration"].get("realTimeTranscription", False)
                                    processing_mode = ProcessingMode.REAL_TIME if real_time_transcription else ProcessingMode.BATCH
                                    self.logger.info(f"Using processing mode: {processing_mode.value} (realTimeTranscription: {real_time_transcription})")
                                else:
                                    self.logger.warning("No active mode configuration found, using BATCH processing mode")
                            except Exception as e:
                                self.logger.error(f"Error determining processing mode from active mode: {e}")
                            
                            # Run the transcription start with the determined processing mode
                            if loop.is_running():
                                # Schedule as task if loop is running
                                asyncio.create_task(transcription_service.start_processing(session_id=session_id, processing_mode=processing_mode))
                            else:
                                # Run until complete if loop is not running
                                loop.run_until_complete(transcription_service.start_processing(session_id=session_id, processing_mode=processing_mode))
                            
                            self.logger.info(f"Transcription service started (session: {session_id})")
                        except Exception as e:
                            self.logger.error(f"Failed to start transcription service: {e}", exc_info=True)
                    
                    # Start transcription in a separate thread to avoid blocking
                    import threading
                    transcription_thread = threading.Thread(target=start_transcription_task, daemon=True)
                    transcription_thread.start()
                    
                except Exception as e:
                    self.logger.error(f"Error starting transcription service: {e}", exc_info=True)
            else:
                self.logger.warning("TranscriptionService not available")
        
        # If audio service failed to start, reset state
        if not success:
            self.is_recording = False
            self.keyboard_manager.is_recording = False
            self.logger.error("Failed to start recording - audio service unavailable")
            return
        
        # Emit recording started event
        self._notify_recording_started(shortcut)
        
        self.logger.info(f"Recording started via {shortcut} shortcut")
    
    def _perform_stop_recording(self, shortcut: str):
        """Perform stop recording action.
        
        Args:
            shortcut: The shortcut that triggered the stop
        """
        # In test mode, only send event and don't stop actual recording
        if self.is_test_mode_active:
            self.logger.info(f"Test mode: Stop recording triggered by {shortcut}")
            self._notify_recording_stopped(shortcut)
            return
        
        # Skip if not recording
        if not self.is_recording:
            return
            
        # Update state
        self.is_recording = False
        self.keyboard_manager.is_recording = False
        
        # RACE CONDITION FIX: Stop AudioService FIRST to prevent new chunks
        # This ensures no new audio chunks are generated before we stop transcription
        recording_result = None
        if self.service_container:
            audio_service = self.service_container.resolve("audio")
            if audio_service:
                # Stop capture first (prevents new chunks from being generated)
                recording_result = audio_service.stop_capture()
                
                if recording_result:
                    self.logger.info(f"Audio capture stopped. Recording saved: {recording_result.get('session_id')}")
                else:
                    self.logger.info("Audio capture stopped (no recording was active)")
            else:
                self.logger.warning("AudioService not available")
        
        # Now stop TranscriptionService (safe now that no new chunks are coming)
        if self.service_container:
            transcription_service = self.service_container.resolve("transcription")
            if transcription_service:
                try:
                    # Use asyncio to call the async stop method
                    import asyncio
                    
                    def stop_transcription_task():
                        try:
                            # Create new event loop for this thread if needed
                            try:
                                loop = asyncio.get_event_loop()
                            except RuntimeError:
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                            
                            # Run the transcription stop
                            if loop.is_running():
                                # Schedule as task if loop is running
                                asyncio.create_task(transcription_service.stop_processing())
                            else:
                                # Run until complete if loop is not running
                                loop.run_until_complete(transcription_service.stop_processing())
                            
                            self.logger.info("Transcription service stopped")
                        except Exception as e:
                            self.logger.error(f"Failed to stop transcription service: {e}", exc_info=True)
                    
                    # Stop transcription in a separate thread to avoid blocking
                    import threading
                    transcription_thread = threading.Thread(target=stop_transcription_task, daemon=True)
                    transcription_thread.start()
                    
                except Exception as e:
                    self.logger.error(f"Error stopping transcription service: {e}", exc_info=True)
            else:
                self.logger.warning("TranscriptionService not available")
        
        # Emit recording stopped event
        self._notify_recording_stopped(shortcut)
        
        self.logger.info(f"Recording stopped via {shortcut} shortcut")
    
    def _perform_cancel_recording(self):
        """Perform cancel recording action."""
        # In test mode, only send event and don't cancel actual recording
        if self.is_test_mode_active:
            self.logger.info("Test mode: Cancel recording triggered")
            self._notify_recording_cancelled()
            return
        
        # Skip if not recording
        if not self.is_recording:
            return
        
        # Skip cancel in push-to-talk mode - user controls recording with press/release
        if self.keyboard_manager.push_to_talk_mode:
            self.logger.debug("Cancel recording ignored in push-to-talk mode")
            return
            
        # Update state
        self.is_recording = False
        self.keyboard_manager.is_recording = False
        
        # Stop TranscriptionService first if available
        if self.service_container:
            transcription_service = self.service_container.resolve("transcription")
            if transcription_service:
                try:
                    # Use asyncio to call the async stop method
                    import asyncio
                    
                    def cancel_transcription_task():
                        try:
                            # Create new event loop for this thread if needed
                            try:
                                loop = asyncio.get_event_loop()
                            except RuntimeError:
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                            
                            # Run the transcription stop (cancellation)
                            if loop.is_running():
                                # Schedule as task if loop is running
                                asyncio.create_task(transcription_service.stop_processing(cancelled=True))
                            else:
                                # Run until complete if loop is not running
                                loop.run_until_complete(transcription_service.stop_processing(cancelled=True))
                            
                            self.logger.info("Transcription service cancelled")
                        except Exception as e:
                            self.logger.error(f"Failed to cancel transcription service: {e}", exc_info=True)
                    
                    # Cancel transcription in a separate thread to avoid blocking
                    import threading
                    transcription_thread = threading.Thread(target=cancel_transcription_task, daemon=True)
                    transcription_thread.start()
                    
                except Exception as e:
                    self.logger.error(f"Error cancelling transcription service: {e}", exc_info=True)
            else:
                self.logger.warning("TranscriptionService not available")
        
        # Stop AudioService capture if running (cancelling recording)
        if self.service_container:
            audio_service = self.service_container.resolve("audio")
            if audio_service:
                # Stop capture (this will also discard any ongoing recording)
                recording_result = audio_service.stop_capture()
                if recording_result:
                    self.logger.info("Audio capture and recording cancelled")
                else:
                    self.logger.info("Audio capture stopped (no recording was active)")
            else:
                self.logger.warning("AudioService not available for cancellation")
        
        # Emit recording cancelled event
        self._notify_recording_cancelled()
        
        self.logger.info("Recording cancelled")
    
    def _perform_mode_switch(self):
        """Perform mode switch action."""
        # In test mode, only send event and don't open the mode switch UI
        if self.is_test_mode_active:
            self.logger.info("Test mode: Mode switch triggered")
            self._notify_mode_switch()
            return
        
        # Enable listening for cancel key after mode switch to allow canceling the mode switch dialog
        self.keyboard_manager.listen_for_mode_switch_cancel = True
        
        # Emit mode switch event to open UI
        self._notify_mode_switch()
        
        self.logger.info("Mode switch triggered - opening mode selection UI")
    
    # Shortcut Recording Management
    def start_shortcut_recording(self, target: str):
        """Start recording shortcuts.
        
        Args:
            target: The target action for the shortcut
        """
        # Skip if already recording shortcuts
        if self.recording_shortcuts:
            return
            
        self.logger.info(f"Starting shortcut recording for {target}")
        
        # Unhook keyboard manager temporarily
        self.keyboard_manager.unhook_all_keys()
        
        # Stop the detection thread if running
        if self.detection_thread and self.detection_thread.is_alive():
            self._stop_thread.set()
            self.detection_thread.join(timeout=1.0)
            
        # Update state
        self.recording_shortcuts = True
        
        # Start recording shortcuts
        self.shortcut_recorder.start_recording(target)
    
    def stop_shortcut_recording(self):
        """Stop recording shortcuts.
        
        Returns:
            List of recorded key names
        """
        if not self.recording_shortcuts:
            return []
            
        self.logger.info("Stopping shortcut recording")
            
        # Stop recording and get the recorded shortcut
        recorded_keys = self.shortcut_recorder.stop_recording()
        
        # Update state
        self.recording_shortcuts = False
        
        # Restore keyboard manager
        self.logger.info("Restoring keyboard hooks")
        self.keyboard_manager.setup_hooks()
        
        # Restart the detection thread
        if not self.detection_thread or not self.detection_thread.is_alive():
            self.logger.debug("Restarting detection thread")
            self._stop_thread.clear()
            self.detection_thread = threading.Thread(
                target=self._run_detection_loop,
                name="ShortcutDetector",
                daemon=True
            )
            self.detection_thread.start()
        
        return recorded_keys
    
    # Event Notification Methods
    def _notify_recording_started(self, shortcut: str):
        """Notify that recording has started.
        
        Args:
            shortcut: The shortcut that triggered the recording
        """
        # Get IPC bridge from service container
        if self.service_container:
            ipc_bridge = self.service_container.resolve("ipc")
            if ipc_bridge:
                # Enable listening for cancel key
                self.keyboard_manager.listen_for_window_cancel = True
                    
                # Send recording started event
                event_message = {
                    "type": "event",
                    "event": "recordingStarted",
                    "data": {
                        "mode": "pushToTalk" if self.keyboard_manager.push_to_talk_mode else "toggle",
                        "shortcut": shortcut
                    }
                }
                ipc_bridge.send_message_sync(event_message)
            else:
                self.logger.warning("No IPC bridge available for recording started notification")
        else:
            self.logger.warning("No service container available for recording started notification")
    
    def _notify_recording_stopped(self, shortcut: str):
        """Notify that recording has stopped.
        
        Args:
            shortcut: The shortcut that triggered the stop
        """
        # Get IPC bridge from service container
        if self.service_container:
            ipc_bridge = self.service_container.resolve("ipc")
            if ipc_bridge:
                # Send recording stopped event
                event_message = {
                    "type": "event",
                    "event": "recordingStopped",
                    "data": {
                        "mode": "pushToTalk" if self.keyboard_manager.push_to_talk_mode else "toggle",
                        "shortcut": shortcut
                    }
                }
                ipc_bridge.send_message_sync(event_message)
            else:
                self.logger.warning("No IPC bridge available for recording stopped notification")
        else:
            self.logger.warning("No service container available for recording stopped notification")
    
    def _notify_recording_cancelled(self):
        """Notify that recording has been cancelled."""
        # Get IPC bridge from service container
        if self.service_container:
            ipc_bridge = self.service_container.resolve("ipc")
            if ipc_bridge:
                # Send recording cancelled event
                event_message = {
                    "type": "event",
                    "event": "recordingCancelled",
                    "data": {
                        "mode": "pushToTalk" if self.keyboard_manager.push_to_talk_mode else "toggle"
                    }
                }
                ipc_bridge.send_message_sync(event_message)
            else:
                self.logger.warning("No IPC bridge available for recording cancelled notification")
        else:
            self.logger.warning("No service container available for recording cancelled notification")
    
    def _notify_mode_switch(self):
        """Notify that the mode switch UI should be opened."""
        # Get IPC bridge from service container
        if self.service_container:
            ipc_bridge = self.service_container.resolve("ipc")
            if ipc_bridge:
                # Send mode switch event to open UI
                event_message = {
                    "type": "event",
                    "event": "modeSwitch",
                    "data": {
                        "timestamp": time.time()
                    }
                }
                ipc_bridge.send_message_sync(event_message)
            else:
                self.logger.warning("No IPC bridge available for mode switch notification")
        else:
            self.logger.warning("No service container available for mode switch notification")
    
    def _notify_window_cancelled(self):
        """Notify that a window action has been cancelled."""
        # Get IPC bridge from service container
        if self.service_container:
            ipc_bridge = self.service_container.resolve("ipc")
            if ipc_bridge:
                # Send window cancelled event
                event_message = {
                    "type": "event",
                    "event": "windowCancelled",
                    "data": {
                        "type": "modeSwitch" if self.keyboard_manager.listen_for_mode_switch_cancel else "shortcut"
                    }
                }
                ipc_bridge.send_message_sync(event_message)
            else:
                self.logger.warning("No IPC bridge available for window cancelled notification")
        else:
            self.logger.warning("No service container available for window cancelled notification")
        
        # Reset flags
        self.keyboard_manager.listen_for_window_cancel = False
        self.keyboard_manager.listen_for_mode_switch_cancel = False
    
    def _on_shortcut_recording_complete(self, target: str, keys: List[str]):
        """Handle completion of shortcut recording.
        
        Args:
            target: The target action that was recorded
            keys: The keys that were recorded
        """
        self.logger.info(f"Shortcut recording completed for {target} with keys: {keys}")
        
        # Automatically stop the recording session to restore keyboard hooks
        self.stop_shortcut_recording() 

    # Service Status
    def get_status(self):
        """Get the service status.
        
        Returns:
            The service status
        """
        # Get recording mode from settings
        settings = self.get_settings()
        recording_mode = settings.get("recordingMode", "pushToTalk")
        push_to_talk = (recording_mode == "pushToTalk")
        
        return {
            "initialized": self._is_initialized,
            "mode": "pushToTalk" if push_to_talk else "toggle",
            "isRecording": self.is_recording,
            "recordingShortcuts": self.recording_shortcuts,
            "testModeActive": self.is_test_mode_active
        }