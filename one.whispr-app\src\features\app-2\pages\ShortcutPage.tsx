/**
 * ShortcutPage Component
 * Complete shortcut configuration page matching app-latest ShortcutsStep functionality
 * Integrates ShortcutContext with sophisticated shortcut recording and testing
 */

import React, { useState, useEffect, useRef } from 'react';
import { useShortcut } from '../contexts/ShortcutContext';
import { Card, CardContent, CardHeader, CardTitle } from '@src/components/ui/card';
import { Button } from '@src/components/ui/button';
import { Alert, AlertDescription } from '@src/components/ui/alert';
import { KeyCombo } from '@src/components/ui/key-combo';
import { KeySymbol } from '@src/components/ui/key-symbol';
import { HiOutlineKey, HiCog6Tooth, HiExclamationCircle, HiCheck, HiMicrophone, HiArrowUturnLeft } from 'react-icons/hi2';

// ============================================================================
// TYPES
// ============================================================================

interface ShortcutPageProps {
  onNext?: () => void;
  onPrevious?: () => void;
  isActive?: boolean;
}

type RecordingMode = 'pushToTalk' | 'toggle';

// Individual shortcut configuration component
const ShortcutConfigItem = ({ 
  label, 
  keys, 
  onEdit, 
  isRecording,
  isBeingRecorded = false,
  tempKeys = []
}: { 
  label: string; 
  keys: string[]; 
  onEdit: () => void; 
  isRecording: boolean;
  isBeingRecorded?: boolean;
  tempKeys?: string[];
}) => {
  // Use temp keys when this specific shortcut is being recorded
  const displayKeys = isBeingRecorded ? tempKeys : keys;
  const hasKeys = displayKeys.length > 0;
  
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/20">
      <span className={`text-sm font-medium ${isBeingRecorded ? 'text-orange-600' : ''}`}>
        {label}
        {isBeingRecorded && <span className="ml-1 text-xs">(Recording...)</span>}
      </span>
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          {hasKeys ? (
            <KeyCombo keyNames={displayKeys} size="md" className="gap-2" />
          ) : (
            <span className={`text-sm ${isBeingRecorded ? 'text-orange-500' : 'text-muted-foreground'}`}>
              {isBeingRecorded ? 'Press keys...' : 'Not set'}
            </span>
          )}
        </div>
        <Button 
          onClick={onEdit} 
          variant="ghost" 
          size="sm"
          disabled={isRecording}
          className="gap-1 hover:bg-muted/50"
        >
          <HiArrowUturnLeft className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

// Shortcut key combo display with live press detection
const ShortcutCombo = ({ 
  keys, 
  pressedKeys, 
  label, 
  isRecording = false, 
  tempKeys = [] 
}: { 
  keys: string[]; 
  pressedKeys: Set<string>; 
  label: string;
  isRecording?: boolean;
  tempKeys?: string[];
}) => {
  // Use tempKeys when recording, otherwise use the configured keys
  const displayKeys = isRecording ? tempKeys : keys;
  const isEmpty = displayKeys.length === 0;
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <HiMicrophone className={`w-5 h-5 ${isRecording ? 'text-orange-500' : 'text-primary'}`} />
        <span className={`text-base font-medium ${isRecording ? 'text-orange-600' : ''}`}>
          {label}
          {isRecording && <span className="ml-2 text-sm">(Recording...)</span>}
        </span>
      </div>
      <div className={`flex items-center justify-center gap-3 p-6 rounded-lg min-h-[120px] transition-all duration-200 ${
        isRecording 
          ? 'bg-orange-50 dark:bg-orange-950/20 border-2 border-orange-200 dark:border-orange-800' 
          : 'bg-muted/20'
      }`}>
        {!isEmpty ? (
          displayKeys.map((keyName, index) => {
            // Direct key matching - Python sends us normalized key names
            const isPressed = pressedKeys.has(keyName);
            
            return (
              <React.Fragment key={keyName}>
                {index > 0 && (
                  <span className={`mx-2 text-2xl font-bold ${isRecording ? 'text-orange-600' : 'text-muted-foreground'}`}>+</span>
                )}
                <div className="transition-all duration-200">
                  <KeySymbol 
                    keyName={keyName}
                    size="setup"
                    className={`
                      transition-all duration-75 font-semibold
                      ${isPressed
                        ? 'border-4 border-green-400 bg-gradient-to-br from-green-400 to-green-500 text-white scale-110 shadow-xl shadow-green-400/50 ring-4 ring-green-300/50' 
                        : isRecording
                        ? 'border-2 border-orange-400 bg-orange-100 text-orange-700'
                        : 'border-2 border-primary-300 bg-primary-50 text-primary-700 hover:border-primary-400'
                      }
                    `}
                  />
                </div>
              </React.Fragment>
            );
          })
        ) : (
          <div className={`text-base ${isRecording ? 'text-orange-600 dark:text-orange-400' : 'text-muted-foreground'}`}>
            {isRecording ? 'Press keys to create your shortcut...' : 'No shortcut configured'}
          </div>
        )}
      </div>
    </div>
  );
};

export function ShortcutPage({ onNext, onPrevious, isActive = false }: ShortcutPageProps) {
  const { 
    state,
    setRecordingMode,
    startRecordingShortcut,
    saveRecordingShortcut,
    cancelRecordingShortcut,
    canProceedToNext,
    enableTestMode,
    disableTestMode,
    saveSettings
  } = useShortcut();

  const {
    recordingMode,
    shortcuts,
    isRecordingShortcut,
    recordingAction,
    tempKeys,
    loading,
    error,
    testModeEnabled
  } = state;

  // State for tracking pressed keys (from Python)
  const [pressedKeys, setPressedKeys] = useState<Set<string>>(new Set());
  
  // Track the active state for test mode
  const isActiveRef = useRef<boolean>(isActive);

  // Handle active state change - similar to AudioPage pattern
  useEffect(() => {
    const handleActiveStateChange = async () => {
      // Only act on state changes, not initial render
      if (isActive !== isActiveRef.current) {
        if (isActive) {
          // Page became active - start test mode
          console.log('⌨️ ShortcutPage: Page became active - starting test mode');
          try {
            await enableTestMode();
          } catch (error) {
            console.error('Failed to enable test mode:', error);
          }
        } else {
          // Page became inactive - stop test mode
          console.log('⌨️ ShortcutPage: Page became inactive - stopping test mode');
          try {
            await disableTestMode();
          } catch (error) {
            console.error('Failed to disable test mode:', error);
          }
        }
        
        // Update the ref to track the current state
        isActiveRef.current = isActive;
      }
    };
    
    // Execute the state change handler
    handleActiveStateChange();
  }, [isActive, enableTestMode, disableTestMode]);

  // Listen for Python keyboard events
  useEffect(() => {
    if (typeof window !== 'undefined' && window.electron?.onBackendMessage) {
      console.log('⌨️ Setting up key event listeners in ShortcutPage');
      
      const cleanup = window.electron.onBackendMessage((message: any) => {
        if (message.type === 'key_event') {
          // Extract key event data
          const currentlyPressed = message.data?.currently_pressed || [];
          
          // Update pressed keys state
          setPressedKeys(new Set(currentlyPressed));
        }
      });

      return () => {
        console.log('⌨️ Cleaning up key event listeners in ShortcutPage');
        cleanup();
      };
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Ensure test mode is disabled when component unmounts
      if (isActiveRef.current) {
        console.log('⌨️ ShortcutPage: Unmounting - ensuring test mode is disabled');
        disableTestMode().catch(error => {
          console.error('Failed to disable test mode during cleanup:', error);
        });
      }
    };
  }, [disableTestMode]);

  const handleModeChange = async (mode: 'pushToTalk' | 'toggle') => {
    setRecordingMode(mode);
    // Save settings to persist the mode change
    try {
      await saveSettings();
    } catch (error) {
      console.error('Failed to save recording mode change:', error);
    }
  };

  const handleEditShortcut = async (action: keyof typeof shortcuts) => {
    try {
      await startRecordingShortcut(action);
    } catch (error) {
      console.error('Failed to start recording shortcut:', error);
    }
  };

  const saveCustomShortcut = async () => {
    saveRecordingShortcut();
    // Save settings to persist the shortcut change
    try {
      await saveSettings();
    } catch (error) {
      console.error('Failed to save shortcut settings:', error);
    }
  };

  const cancelCustomizing = () => {
    cancelRecordingShortcut();
  };

  const handleContinue = () => {
    if (canProceedToNext && onNext) {
      onNext();
    }
  };

  // Convert recordingAction to recordingCustomShortcut for compatibility
  const recordingCustomShortcut = recordingAction === 'pushToTalk' ? 'pushToTalk' : 
                                 recordingAction === 'toggle' ? 'toggle' : null;

  return (
    <div className="h-full flex items-center justify-center px-6">
      <div className="max-w-4xl w-full space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-semibold text-foreground">Keyboard Shortcuts</h2>
          <p className="text-muted-foreground">
            Choose your recording mode and test your shortcuts
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <HiExclamationCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Two Cards Side by Side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center">
          {/* Shortcut Visualization Card */}
          <Card className="self-center">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HiOutlineKey className="w-5 h-5" />
                Key Combinations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {recordingMode === 'pushToTalk' ? (
                // Push-to-talk mode shortcuts
                <ShortcutCombo
                  keys={shortcuts.pushToTalk}
                  pressedKeys={pressedKeys}
                  label="Push-to-Talk"
                  isRecording={recordingCustomShortcut === 'pushToTalk'}
                  tempKeys={tempKeys}
                />
              ) : (
                // Toggle mode shortcuts
                <>
                  <ShortcutCombo
                    keys={shortcuts.toggle}
                    pressedKeys={pressedKeys}
                    label="Toggle Recording"
                    isRecording={recordingCustomShortcut === 'toggle'}
                    tempKeys={tempKeys}
                  />
                  <ShortcutCombo
                    keys={shortcuts.cancel}
                    pressedKeys={pressedKeys}
                    label="Cancel Recording"
                    isRecording={false}
                    tempKeys={[]}
                  />
                </>
              )}

              {/* Instructions */}
              {!recordingCustomShortcut && (
                <div className="text-center p-4 bg-muted/10 rounded-lg border border-dashed border-muted-foreground/20">
                  <p className="text-sm text-muted-foreground mb-2">
                    {shortcuts.pushToTalk.length > 0 || shortcuts.toggle.length > 0 ? (
                      <>
                        <span className="font-medium">Test your shortcuts:</span><br />
                        Press the keys above to see them light up in real-time
                      </>
                    ) : (
                      <>
                        <span className="font-medium">No shortcuts configured yet</span><br />
                        Use the configuration panel to set up your shortcuts
                      </>
                    )}
                  </p>
                </div>
              )}

              {/* Show save/cancel buttons when recording */}
              {recordingCustomShortcut && (
                <div className="flex gap-2 justify-center">
                  <Button 
                    onClick={saveCustomShortcut} 
                    size="sm" 
                    disabled={tempKeys.length === 0}
                    className="gap-1"
                  >
                    <HiCheck className="w-4 h-4" />
                    Save
                  </Button>
                  <Button 
                    onClick={cancelCustomizing} 
                    variant="outline" 
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>
              )}

            </CardContent>
          </Card>

          {/* Configuration Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HiCog6Tooth className="w-5 h-5" />
                Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Recording Mode Selection - Horizontal */}
              <div>
                <label className="text-base font-medium mb-4 block">Recording Mode</label>
                <div className="grid grid-cols-2 gap-4">
                  <div className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      recordingMode === 'pushToTalk' 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => handleModeChange('pushToTalk')}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <div className={`w-3 h-3 rounded-full border-2 ${
                        recordingMode === 'pushToTalk' 
                          ? 'border-primary bg-primary' 
                          : 'border-border'
                      }`} />
                      <span className="font-medium">Push-to-Talk</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Hold to record, release to stop
                    </p>
                  </div>
                  
                  <div 
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      recordingMode === 'toggle' 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => handleModeChange('toggle')}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <div className={`w-3 h-3 rounded-full border-2 ${
                        recordingMode === 'toggle' 
                          ? 'border-primary bg-primary' 
                          : 'border-border'
                      }`} />
                      <span className="font-medium">Toggle Mode</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Press once to start, again to stop
                    </p>
                  </div>
                </div>
              </div>

              {/* Individual Shortcut Configuration */}
              <div>
                <label className="text-base font-medium mb-4 block">Configure Shortcuts</label>
                <div className="space-y-4">
                  {recordingMode === 'pushToTalk' ? (
                    // Push-to-talk mode: show only pushtotalk
                    <ShortcutConfigItem
                      label="Push-to-Talk"
                      keys={shortcuts.pushToTalk}
                      onEdit={() => handleEditShortcut('pushToTalk')}
                      isRecording={recordingCustomShortcut !== null}
                      isBeingRecorded={recordingCustomShortcut === 'pushToTalk'}
                      tempKeys={tempKeys}
                    />
                  ) : (
                    // Toggle mode: show toggle and cancel
                    <>
                      <ShortcutConfigItem
                        label="Toggle Recording"
                        keys={shortcuts.toggle}
                        onEdit={() => handleEditShortcut('toggle')}
                        isRecording={recordingCustomShortcut !== null}
                        isBeingRecorded={recordingCustomShortcut === 'toggle'}
                        tempKeys={tempKeys}
                      />
                      <ShortcutConfigItem
                        label="Cancel Recording"
                        keys={shortcuts.cancel}
                        onEdit={() => handleEditShortcut('cancel')}
                        isRecording={recordingCustomShortcut !== null}
                        isBeingRecorded={false} // Cancel is never recorded directly
                        tempKeys={[]}
                      />
                    </>
                  )}
                </div>
              </div>

              {/* Navigation */}
              <div className="flex justify-between">
                <Button 
                  onClick={onPrevious} 
                  variant="outline" 
                  disabled={!onPrevious || recordingCustomShortcut !== null}
                >
                  Previous
                </Button>
                <Button 
                  onClick={handleContinue}
                  disabled={recordingCustomShortcut !== null}
                >
                  Continue
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 