import { ElectronAPI } from '../../electron/preload';

declare global {
  interface Window {
    electron: ElectronAPI;
  }
} 

/*
import { ElectronAPI } from '../../electron/preload';

// Types for authentication callback data
interface AuthCallbackData {
  token?: string;
  email?: string;
}

// Types for Python status data
interface PythonStatusData {
  pythonRunning: boolean;
  pythonConnected: boolean;
  port: number | null;
}

// Types for auth URLs
interface AuthUrls {
  login: string;
  register: string;
}

// Main Electron API interface
interface ElectronAPI {
  // Authentication flow methods
  openExternalUrl: (url: string) => Promise<boolean>;
  getAuthUrls: () => Promise<AuthUrls>;
  onAuthCallback: (callback: (data: AuthCallbackData) => void) => () => void;

  // Python backend communication
  sendCommand: (type: string, args?: any) => Promise<any>;
  getPythonStatus: () => Promise<PythonStatusData>;
  reconnectPython: () => Promise<{ status: string; pythonConnected: boolean }>;

  // Python WebSocket message listeners
  onBackendMessage: (callback: (message: any) => void) => () => void;
  onBackendConnected: (callback: () => void) => () => void;
  onBackendConnectionFailed: (callback: () => void) => () => void;
  onBackendServerWelcome: (callback: (data: any) => void) => () => void;
}

declare global {
  interface Window {
    electron: ElectronAPI;
  }
}

export type { AuthCallbackData, PythonStatusData, AuthUrls, ElectronAPI }; 

*/