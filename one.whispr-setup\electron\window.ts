import { BrowserWindow, screen, app, nativeImage } from 'electron';
import path from 'path';
import fs from 'fs';

// Extend the App interface
interface ExtendedApp extends Electron.App {
  isQuitting?: boolean;
}

let launcherWindow: BrowserWindow | null = null;

export function createLauncherWindow(): BrowserWindow {
  // Get primary display to center the window
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;
  
  // Small window size for launcher
  const windowWidth = 300;
  const windowHeight = 300;
  
  // Calculate center position
  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round((screenHeight - windowHeight) / 2);

  launcherWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    resizable: false,
    movable: false,
    minimizable: false,
    maximizable: false,
    closable: false,
    skipTaskbar: false,
    icon: nativeImage.createFromPath(
      app.isPackaged
        ? path.join(process.resourcesPath, 'icon.ico')
        : path.join(process.cwd(), 'src', 'assets', 'icon.ico')
    ),
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
      backgroundThrottling: false
    },
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#00000000',
      symbolColor: '#00000000',
      height: 0,
    },
    show: false,
    frame: false // Remove window frame for clean launcher look
  });

  launcherWindow.once('ready-to-show', () => {
    console.log('[LAUNCHER] Window ready');
  });

  // Prevent closing - launcher should stay open until app launches
  launcherWindow.on('close', (e) => {
    if (!(app as ExtendedApp).isQuitting) {
      e.preventDefault();
    }
  });

  // Always load from built files (no hot reload issues)
  console.log('[LAUNCHER] Loading from built files');
  const htmlPath = path.join(__dirname, '../../renderer/index.html');
  
  if (!fs.existsSync(htmlPath)) {
    console.error('[LAUNCHER] Could not find HTML file at:', htmlPath);
    throw new Error(`HTML file not found at ${htmlPath}. Run 'npm run build:vite' first.`);
  }
  
  // Open dev tools in development for debugging
  if (process.env.NODE_ENV === 'development') {
    launcherWindow.webContents.openDevTools();
  }
  
  launcherWindow.loadFile(htmlPath);

  return launcherWindow;
}

export function getLauncherWindow(): BrowserWindow | null {
  return launcherWindow;
}

export function showLauncherWindow(): void {
  if (launcherWindow) {
    console.log('[LAUNCHER] Showing launcher window');
    launcherWindow.show();
    launcherWindow.focus();
  } else {
    console.warn('[LAUNCHER] Cannot show window - not created yet');
  }
}

export function closeLauncherWindow(): void {
  if (launcherWindow) {
    console.log('[LAUNCHER] Closing launcher window');
    (app as ExtendedApp).isQuitting = true;
    launcherWindow.close();
    launcherWindow = null;
  }
}